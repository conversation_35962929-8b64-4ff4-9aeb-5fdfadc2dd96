"""
Advanced Aerodynamics System
============================

Realistic aerodynamic modeling including drag, downforce, and side forces.
"""

import math
import numpy as np
from panda3d.core import Vec3
from utils.math_utils import MathUtils

class AerodynamicsSystem:
    """Advanced aerodynamics simulation."""
    
    def __init__(self, car_physics):
        """Initialize aerodynamics system."""
        self.car_physics = car_physics
        
        # Vehicle aerodynamic properties
        self.frontal_area = 2.2  # m² (typical sports car)
        self.drag_coefficient = 0.32  # Cd
        self.lift_coefficient = 0.1   # Cl (positive = lift, negative = downforce)
        
        # Downforce components
        self.front_wing_area = 0.5    # m²
        self.rear_wing_area = 0.8     # m²
        self.front_wing_cl = -1.2     # Negative = downforce
        self.rear_wing_cl = -1.8      # Negative = downforce
        self.underbody_cl = -0.3      # Ground effect
        
        # Side force (crosswind sensitivity)
        self.side_area = 4.5          # m² (side profile area)
        self.side_force_coefficient = 0.8
        
        # Air properties
        self.air_density = 1.225      # kg/m³ (sea level, 15°C)
        
        # Aerodynamic balance
        self.front_downforce_ratio = 0.4  # 40% front, 60% rear
        
        # Ground effect parameters
        self.ground_effect_height = 0.15  # meters (ride height for max ground effect)
        self.ground_effect_factor = 1.5   # Multiplier for downforce near ground
        
        # Aerodynamic center positions (relative to CG)
        self.drag_center = Vec3(0, 0.2, 0.3)      # Slightly forward and up
        self.downforce_center = Vec3(0, -0.1, 0.1) # Slightly behind CG
        
        print("Aerodynamics system initialized")
    
    def calculate_air_velocity(self, vehicle_velocity, wind_velocity=Vec3(0, 0, 0)):
        """Calculate relative air velocity."""
        # Air velocity relative to vehicle
        relative_air_velocity = vehicle_velocity - wind_velocity
        return relative_air_velocity
    
    def calculate_ground_effect_factor(self):
        """Calculate ground effect multiplier based on ride height."""
        ride_height = self.car_physics.suspension.get_ride_height()
        
        if ride_height <= 0:
            return 1.0
        
        # Ground effect decreases exponentially with height
        effect_factor = math.exp(-ride_height / self.ground_effect_height)
        
        # Scale between 1.0 (no effect) and ground_effect_factor (max effect)
        return 1.0 + (self.ground_effect_factor - 1.0) * effect_factor
    
    def calculate_drag_force(self, air_velocity):
        """Calculate aerodynamic drag force."""
        speed_squared = air_velocity.lengthSquared()
        if speed_squared < 0.01:
            return Vec3(0, 0, 0)
        
        # Drag magnitude
        drag_magnitude = 0.5 * self.air_density * self.drag_coefficient * self.frontal_area * speed_squared
        
        # Drag direction (opposite to air velocity)
        drag_direction = air_velocity.normalized() * -1
        
        return drag_direction * drag_magnitude
    
    def calculate_downforce(self, air_velocity):
        """Calculate total downforce from all aerodynamic components."""
        speed_squared = air_velocity.lengthSquared()
        if speed_squared < 0.01:
            return Vec3(0, 0, 0)
        
        # Ground effect factor
        ground_effect = self.calculate_ground_effect_factor()
        
        # Front wing downforce
        front_downforce = (0.5 * self.air_density * abs(self.front_wing_cl) * 
                          self.front_wing_area * speed_squared)
        
        # Rear wing downforce
        rear_downforce = (0.5 * self.air_density * abs(self.rear_wing_cl) * 
                         self.rear_wing_area * speed_squared)
        
        # Underbody downforce (affected by ground effect)
        underbody_downforce = (0.5 * self.air_density * abs(self.underbody_cl) * 
                              self.frontal_area * speed_squared * ground_effect)
        
        # Total downforce
        total_downforce = front_downforce + rear_downforce + underbody_downforce
        
        return Vec3(0, 0, -total_downforce)
    
    def calculate_side_force(self, air_velocity):
        """Calculate side force from crosswind."""
        # Side force is primarily from lateral air velocity
        lateral_velocity = air_velocity.x
        
        if abs(lateral_velocity) < 0.1:
            return Vec3(0, 0, 0)
        
        # Side force magnitude
        side_force_magnitude = (0.5 * self.air_density * self.side_force_coefficient * 
                               self.side_area * lateral_velocity**2)
        
        # Force direction
        if lateral_velocity > 0:
            side_force = Vec3(-side_force_magnitude, 0, 0)
        else:
            side_force = Vec3(side_force_magnitude, 0, 0)
        
        return side_force
    
    def calculate_pitch_moment(self, air_velocity, downforce):
        """Calculate pitching moment from aerodynamic forces."""
        speed_squared = air_velocity.lengthSquared()
        if speed_squared < 0.01:
            return 0.0
        
        # Front and rear downforce distribution
        front_downforce = abs(downforce.z) * self.front_downforce_ratio
        rear_downforce = abs(downforce.z) * (1.0 - self.front_downforce_ratio)
        
        # Moment arms (distance from CG)
        front_moment_arm = self.car_physics.wheelbase / 2
        rear_moment_arm = -self.car_physics.wheelbase / 2
        
        # Pitching moment (positive = nose up)
        pitch_moment = (front_downforce * front_moment_arm + 
                       rear_downforce * rear_moment_arm)
        
        return pitch_moment
    
    def calculate_yaw_moment(self, air_velocity, side_force):
        """Calculate yaw moment from side forces."""
        # Simplified yaw moment calculation
        # In reality, this would be much more complex
        
        if abs(side_force.x) < 0.1:
            return 0.0
        
        # Moment arm (distance from CG to aerodynamic center)
        moment_arm = 0.5  # meters behind CG
        
        yaw_moment = side_force.x * moment_arm
        
        return yaw_moment
    
    def calculate_forces(self, vehicle_velocity, wind_velocity=Vec3(0, 0, 0)):
        """
        Calculate all aerodynamic forces and moments.
        
        Args:
            vehicle_velocity: Vehicle velocity vector
            wind_velocity: Wind velocity vector
        
        Returns:
            Tuple of (total_force, total_torque)
        """
        # Calculate relative air velocity
        air_velocity = self.calculate_air_velocity(vehicle_velocity, wind_velocity)
        
        # Calculate individual force components
        drag_force = self.calculate_drag_force(air_velocity)
        downforce = self.calculate_downforce(air_velocity)
        side_force = self.calculate_side_force(air_velocity)
        
        # Total aerodynamic force
        total_force = drag_force + downforce + side_force
        
        # Calculate moments
        pitch_moment = self.calculate_pitch_moment(air_velocity, downforce)
        yaw_moment = self.calculate_yaw_moment(air_velocity, side_force)
        
        # Total torque vector
        total_torque = Vec3(0, pitch_moment, yaw_moment)
        
        return total_force, total_torque
    
    def adjust_aerodynamic_settings(self, front_wing_angle=0, rear_wing_angle=0):
        """
        Adjust aerodynamic settings (wing angles, etc.).
        
        Args:
            front_wing_angle: Front wing angle in degrees (positive = more downforce)
            rear_wing_angle: Rear wing angle in degrees (positive = more downforce)
        """
        # Convert angles to lift coefficient changes
        # Simplified relationship: 1 degree = 0.1 change in Cl
        front_cl_change = front_wing_angle * 0.1
        rear_cl_change = rear_wing_angle * 0.1
        
        self.front_wing_cl -= front_cl_change  # More negative = more downforce
        self.rear_wing_cl -= rear_cl_change
        
        # Update aerodynamic balance
        total_downforce_change = front_cl_change + rear_cl_change
        if total_downforce_change != 0:
            self.front_downforce_ratio = (abs(self.front_wing_cl) / 
                                        (abs(self.front_wing_cl) + abs(self.rear_wing_cl)))
        
        print(f"Aero settings adjusted: Front wing {front_wing_angle}°, Rear wing {rear_wing_angle}°")
        print(f"New balance: {self.front_downforce_ratio:.1%} front")
    
    def get_aerodynamic_telemetry(self, vehicle_velocity):
        """Get aerodynamic telemetry data."""
        air_velocity = self.calculate_air_velocity(vehicle_velocity)
        drag_force = self.calculate_drag_force(air_velocity)
        downforce = self.calculate_downforce(air_velocity)
        side_force = self.calculate_side_force(air_velocity)
        
        return {
            'air_speed': air_velocity.length(),
            'drag_force': drag_force.length(),
            'downforce': abs(downforce.z),
            'side_force': abs(side_force.x),
            'ground_effect_factor': self.calculate_ground_effect_factor(),
            'front_downforce_ratio': self.front_downforce_ratio,
            'drag_coefficient': self.drag_coefficient,
            'front_wing_cl': self.front_wing_cl,
            'rear_wing_cl': self.rear_wing_cl
        }
    
    def set_environmental_conditions(self, air_density=None, temperature=None, pressure=None):
        """Set environmental conditions affecting aerodynamics."""
        if air_density is not None:
            self.air_density = air_density
        elif temperature is not None and pressure is not None:
            # Calculate air density from temperature and pressure
            # Using ideal gas law: ρ = P / (R * T)
            R = 287.05  # Specific gas constant for dry air (J/kg⋅K)
            T_kelvin = temperature + 273.15
            self.air_density = pressure / (R * T_kelvin)
        
        print(f"Air density set to {self.air_density:.3f} kg/m³")

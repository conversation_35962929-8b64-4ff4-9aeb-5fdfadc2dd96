"""
Mathematical Utilities for Racing Simulation
===========================================

Advanced mathematical functions for physics calculations.
"""

import numpy as np
from panda3d.core import Vec3, <PERSON>ua<PERSON>, Mat4
import math

class MathUtils:
    """Collection of mathematical utility functions."""
    
    @staticmethod
    def clamp(value, min_val, max_val):
        """Clamp a value between min and max."""
        return max(min_val, min(value, max_val))
    
    @staticmethod
    def lerp(a, b, t):
        """Linear interpolation between a and b."""
        return a + (b - a) * t
    
    @staticmethod
    def smooth_step(edge0, edge1, x):
        """Smooth step function for smooth transitions."""
        t = MathUtils.clamp((x - edge0) / (edge1 - edge0), 0.0, 1.0)
        return t * t * (3.0 - 2.0 * t)
    
    @staticmethod
    def vec3_to_numpy(vec3):
        """Convert Panda3D Vec3 to numpy array."""
        return np.array([vec3.x, vec3.y, vec3.z])
    
    @staticmethod
    def numpy_to_vec3(arr):
        """Convert numpy array to Panda3D Vec3."""
        return Vec3(float(arr[0]), float(arr[1]), float(arr[2]))
    
    @staticmethod
    def normalize_angle(angle):
        """Normalize angle to [-pi, pi] range."""
        while angle > math.pi:
            angle -= 2 * math.pi
        while angle < -math.pi:
            angle += 2 * math.pi
        return angle
    
    @staticmethod
    def angle_difference(a1, a2):
        """Calculate the shortest angular difference between two angles."""
        diff = a2 - a1
        return MathUtils.normalize_angle(diff)
    
    @staticmethod
    def calculate_tire_slip_angle(velocity, angular_velocity, wheel_angle=0):
        """Calculate tire slip angle for physics simulation."""
        if abs(velocity.x) < 0.1:
            return 0.0
        
        # Calculate slip angle
        slip_angle = math.atan2(velocity.y, abs(velocity.x)) - wheel_angle
        return MathUtils.normalize_angle(slip_angle)
    
    @staticmethod
    def calculate_tire_slip_ratio(wheel_speed, vehicle_speed):
        """Calculate tire slip ratio for traction modeling."""
        if abs(vehicle_speed) < 0.1:
            return 0.0
        
        slip_ratio = (wheel_speed - vehicle_speed) / abs(vehicle_speed)
        return MathUtils.clamp(slip_ratio, -1.0, 1.0)
    
    @staticmethod
    def pacejka_tire_model(slip_angle, normal_force, friction_coeff=1.0):
        """
        Simplified Pacejka tire model for lateral force calculation.
        
        Args:
            slip_angle: Tire slip angle in radians
            normal_force: Normal force on tire in Newtons
            friction_coeff: Friction coefficient
        
        Returns:
            Lateral force in Newtons
        """
        # Pacejka parameters (simplified)
        B = 10.0  # Stiffness factor
        C = 1.3   # Shape factor
        D = friction_coeff * normal_force  # Peak factor
        E = 0.97  # Curvature factor
        
        # Calculate lateral force
        lateral_force = D * math.sin(C * math.atan(B * slip_angle - E * (B * slip_angle - math.atan(B * slip_angle))))
        
        return lateral_force
    
    @staticmethod
    def calculate_aerodynamic_force(velocity, air_density, drag_coeff, frontal_area):
        """Calculate aerodynamic drag force."""
        speed_squared = velocity.lengthSquared()
        if speed_squared < 0.01:
            return Vec3(0, 0, 0)
        
        # Drag force magnitude
        drag_magnitude = 0.5 * air_density * drag_coeff * frontal_area * speed_squared
        
        # Drag direction (opposite to velocity)
        drag_direction = velocity.normalized() * -1
        
        return drag_direction * drag_magnitude
    
    @staticmethod
    def calculate_downforce(velocity, air_density, downforce_coeff, wing_area):
        """Calculate aerodynamic downforce."""
        speed_squared = velocity.lengthSquared()
        downforce_magnitude = 0.5 * air_density * downforce_coeff * wing_area * speed_squared
        
        return Vec3(0, 0, -downforce_magnitude)
    
    @staticmethod
    def spring_damper_force(displacement, velocity, spring_rate, damping_rate):
        """Calculate spring-damper force for suspension."""
        spring_force = -spring_rate * displacement
        damper_force = -damping_rate * velocity
        return spring_force + damper_force
    
    @staticmethod
    def calculate_weight_transfer(acceleration, mass, wheelbase, cg_height):
        """Calculate weight transfer during acceleration/braking."""
        # Longitudinal weight transfer
        long_transfer = (mass * acceleration.y * cg_height) / wheelbase
        
        # Lateral weight transfer (simplified)
        lat_transfer = (mass * acceleration.x * cg_height) / wheelbase
        
        return Vec3(lat_transfer, long_transfer, 0)
    
    @staticmethod
    def engine_torque_curve(rpm, max_torque=400, max_power_rpm=6000, redline=7500):
        """
        Calculate engine torque based on RPM using a realistic curve.
        
        Args:
            rpm: Engine RPM
            max_torque: Maximum torque in Nm
            max_power_rpm: RPM at maximum power
            redline: Maximum RPM
        
        Returns:
            Torque in Nm
        """
        if rpm <= 0:
            return 0
        
        if rpm > redline:
            return 0
        
        # Normalized RPM
        norm_rpm = rpm / max_power_rpm
        
        # Torque curve approximation
        if norm_rpm <= 1.0:
            # Rising torque curve
            torque_factor = 0.8 + 0.2 * (1.0 - (norm_rpm - 0.5) ** 2)
        else:
            # Falling torque curve after peak power
            torque_factor = max(0.0, 1.0 - (norm_rpm - 1.0) * 0.5)
        
        return max_torque * torque_factor
    
    @staticmethod
    def gear_ratio_calculation(gear, gear_ratios, final_drive=3.73):
        """Calculate total gear ratio."""
        if gear == 0:  # Neutral
            return 0
        elif gear == -1:  # Reverse
            return -gear_ratios[0] * final_drive
        else:
            return gear_ratios[gear - 1] * final_drive
    
    @staticmethod
    def calculate_rolling_resistance(normal_force, rolling_coeff=0.015):
        """Calculate rolling resistance force."""
        return normal_force * rolling_coeff

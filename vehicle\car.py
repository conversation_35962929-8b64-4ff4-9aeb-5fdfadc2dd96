"""
Car Vehicle System
=================

Main car class that integrates all physics and visual systems.
"""

import math
import numpy as np
from panda3d.core import *
from panda3d.bullet import *
from direct.task import Task

from physics.car_physics import CarPhysics
from utils.math_utils import MathUtils

class Car:
    """Main car class integrating all systems."""
    
    def __init__(self, game_engine, position=Vec3(0, 0, 0)):
        """Initialize car."""
        self.game_engine = game_engine
        
        # Car physics
        self.physics = CarPhysics()
        self.physics.position = position
        
        # Visual representation
        self.visual_node = None
        self.wheel_nodes = []
        
        # Camera system
        self.camera_mode = "chase"  # chase, cockpit, hood, wheel
        self.camera_distance = 8.0
        self.camera_height = 3.0
        self.camera_smoothing = 0.1
        
        # Create car visual and physics
        self.create_car_visual()
        self.create_car_physics_body()
        
        # Performance data
        self.lap_time = 0.0
        self.best_lap = float('inf')
        self.sector_times = []
        
        print("Car initialized")
    
    def create_car_visual(self):
        """Create procedural car visual representation."""
        # Create main car body
        car_body = self.create_car_body_mesh()
        self.visual_node = self.game_engine.render.attachNewNode(car_body)
        
        # Create wheels
        self.create_wheel_visuals()
        
        # Apply materials and textures
        self.apply_car_materials()
        
        # Position car
        self.visual_node.setPos(self.physics.position)
    
    def create_car_body_mesh(self):
        """Create procedural car body mesh."""
        # Car dimensions
        length = 4.5  # meters
        width = 1.8
        height = 1.3
        
        # Create car body using CardMaker (simplified)
        # In a full implementation, this would be a proper 3D mesh
        format = GeomVertexFormat.getV3n3()
        vdata = GeomVertexData('car', format, Geom.UHStatic)
        
        # Create a simple box-shaped car
        vertices = [
            # Front face
            (-width/2, length/2, 0), (width/2, length/2, 0),
            (width/2, length/2, height), (-width/2, length/2, height),
            # Back face
            (-width/2, -length/2, 0), (width/2, -length/2, 0),
            (width/2, -length/2, height), (-width/2, -length/2, height),
        ]
        
        vdata.setNumRows(len(vertices))
        vertex_writer = GeomVertexWriter(vdata, 'vertex')
        normal_writer = GeomVertexWriter(vdata, 'normal')
        
        for vertex in vertices:
            vertex_writer.addData3(*vertex)
            normal_writer.addData3(0, 0, 1)  # Simplified normals
        
        # Create geometry
        geom = Geom(vdata)
        
        # Add triangles (simplified box)
        triangles = GeomTriangles(Geom.UHStatic)
        
        # Front face
        triangles.addVertices(0, 1, 2)
        triangles.addVertices(0, 2, 3)
        # Back face
        triangles.addVertices(4, 6, 5)
        triangles.addVertices(4, 7, 6)
        # Top face
        triangles.addVertices(3, 2, 6)
        triangles.addVertices(3, 6, 7)
        # Bottom face
        triangles.addVertices(0, 5, 1)
        triangles.addVertices(0, 4, 5)
        # Left face
        triangles.addVertices(0, 3, 7)
        triangles.addVertices(0, 7, 4)
        # Right face
        triangles.addVertices(1, 6, 2)
        triangles.addVertices(1, 5, 6)
        
        triangles.closePrimitive()
        geom.addPrimitive(triangles)
        
        # Create geom node
        geom_node = GeomNode('car_body')
        geom_node.addGeom(geom)
        
        return geom_node
    
    def create_wheel_visuals(self):
        """Create visual representations of wheels."""
        wheel_radius = self.physics.tire_physics.tire_radius
        wheel_width = self.physics.tire_physics.tire_width
        
        for i, wheel_pos in enumerate(self.physics.wheel_positions):
            # Create wheel geometry (cylinder)
            wheel_geom = self.create_wheel_mesh(wheel_radius, wheel_width)
            wheel_node = self.visual_node.attachNewNode(wheel_geom)
            wheel_node.setPos(wheel_pos)
            
            # Rotate wheel to correct orientation
            wheel_node.setHpr(90, 0, 0)  # Rotate to align with car
            
            self.wheel_nodes.append(wheel_node)
    
    def create_wheel_mesh(self, radius, width):
        """Create a procedural wheel mesh."""
        # Simplified wheel (cylinder)
        # In a full implementation, this would be a detailed wheel model
        
        format = GeomVertexFormat.getV3n3()
        vdata = GeomVertexData('wheel', format, Geom.UHStatic)
        
        segments = 16
        vdata.setNumRows(segments * 2 + 2)  # Top and bottom circles + centers
        
        vertex_writer = GeomVertexWriter(vdata, 'vertex')
        normal_writer = GeomVertexWriter(vdata, 'normal')
        
        # Create cylinder vertices
        for i in range(segments):
            angle = (i / segments) * 2 * math.pi
            x = radius * math.cos(angle)
            z = radius * math.sin(angle)
            
            # Top circle
            vertex_writer.addData3(x, width/2, z)
            normal_writer.addData3(0, 1, 0)
            
            # Bottom circle
            vertex_writer.addData3(x, -width/2, z)
            normal_writer.addData3(0, -1, 0)
        
        # Center points
        vertex_writer.addData3(0, width/2, 0)   # Top center
        normal_writer.addData3(0, 1, 0)
        vertex_writer.addData3(0, -width/2, 0)  # Bottom center
        normal_writer.addData3(0, -1, 0)
        
        # Create geometry
        geom = Geom(vdata)
        triangles = GeomTriangles(Geom.UHStatic)
        
        # Side faces
        for i in range(segments):
            next_i = (i + 1) % segments
            
            # Side triangles
            triangles.addVertices(i*2, next_i*2, i*2+1)
            triangles.addVertices(next_i*2, next_i*2+1, i*2+1)
        
        # Top and bottom faces
        top_center = segments * 2
        bottom_center = segments * 2 + 1
        
        for i in range(segments):
            next_i = (i + 1) % segments
            
            # Top face
            triangles.addVertices(top_center, i*2, next_i*2)
            # Bottom face
            triangles.addVertices(bottom_center, next_i*2+1, i*2+1)
        
        triangles.closePrimitive()
        geom.addPrimitive(triangles)
        
        geom_node = GeomNode('wheel')
        geom_node.addGeom(geom)
        
        return geom_node
    
    def apply_car_materials(self):
        """Apply materials and colors to car."""
        # Car body material
        body_material = Material()
        body_material.setShininess(32.0)
        body_material.setAmbient(VBase4(0.2, 0.2, 0.2, 1))
        body_material.setDiffuse(VBase4(0.8, 0.1, 0.1, 1))  # Red car
        body_material.setSpecular(VBase4(0.5, 0.5, 0.5, 1))
        
        self.visual_node.setMaterial(body_material)
        
        # Wheel materials
        wheel_material = Material()
        wheel_material.setShininess(8.0)
        wheel_material.setAmbient(VBase4(0.1, 0.1, 0.1, 1))
        wheel_material.setDiffuse(VBase4(0.2, 0.2, 0.2, 1))  # Dark wheels
        wheel_material.setSpecular(VBase4(0.1, 0.1, 0.1, 1))
        
        for wheel_node in self.wheel_nodes:
            wheel_node.setMaterial(wheel_material)
    
    def create_car_physics_body(self):
        """Create physics body for the car."""
        # Create box shape for car body
        car_shape = BulletBoxShape(Vec3(0.9, 2.25, 0.65))  # Half extents
        
        # Create rigid body
        self.physics_body = BulletRigidBodyNode('car')
        self.physics_body.setMass(self.physics.mass)
        self.physics_body.addShape(car_shape)
        
        # Set physics properties
        self.physics_body.setLinearDamping(0.1)
        self.physics_body.setAngularDamping(0.1)
        self.physics_body.setFriction(0.8)
        self.physics_body.setRestitution(0.1)
        
        # Add to physics world
        self.physics_body_np = self.game_engine.render.attachNewNode(self.physics_body)
        self.physics_body_np.setPos(self.physics.position)
        self.game_engine.physics_world.attachRigidBody(self.physics_body)
    
    def set_control_inputs(self, throttle, brake, steering, clutch=0.0):
        """Set control inputs for the car."""
        self.physics.set_control_inputs(throttle, brake, steering, clutch)
    
    def update_visual_from_physics(self):
        """Update visual representation from physics state."""
        # Update car body position and orientation
        self.visual_node.setPos(self.physics.position)
        self.visual_node.setQuat(self.physics.orientation)
        
        # Update wheel rotations
        for i, wheel_node in enumerate(self.wheel_nodes):
            # Rotate wheel based on speed
            wheel_speed = self.physics.wheel_speeds[i]
            wheel_radius = self.physics.tire_physics.tire_radius
            
            if wheel_radius > 0:
                rotation_speed = wheel_speed / wheel_radius  # rad/s
                current_rotation = wheel_node.getH()
                new_rotation = current_rotation + math.degrees(rotation_speed) * globalClock.getDt()
                wheel_node.setH(new_rotation)
            
            # Update steering angle for front wheels
            if i < 2:  # Front wheels
                steering_angle = math.degrees(self.physics.wheel_angles[i])
                wheel_node.setP(steering_angle)
    
    def update_camera(self, camera):
        """Update camera to follow the car."""
        if self.camera_mode == "chase":
            self.update_chase_camera(camera)
        elif self.camera_mode == "cockpit":
            self.update_cockpit_camera(camera)
        elif self.camera_mode == "hood":
            self.update_hood_camera(camera)
    
    def update_chase_camera(self, camera):
        """Update chase camera behind the car."""
        # Calculate target camera position
        car_pos = self.physics.position
        car_orientation = self.physics.orientation
        
        # Camera offset in car's local coordinates
        local_offset = Vec3(0, -self.camera_distance, self.camera_height)
        world_offset = car_orientation.xform(local_offset)
        
        target_pos = car_pos + world_offset
        
        # Smooth camera movement
        current_pos = camera.getPos()
        new_pos = current_pos + (target_pos - current_pos) * self.camera_smoothing
        camera.setPos(new_pos)
        
        # Look at car
        camera.lookAt(car_pos + Vec3(0, 0, 1))
    
    def update_cockpit_camera(self, camera):
        """Update cockpit camera inside the car."""
        car_pos = self.physics.position
        car_orientation = self.physics.orientation
        
        # Camera position inside car
        local_offset = Vec3(0, 0.5, 0.8)  # Driver's eye position
        world_offset = car_orientation.xform(local_offset)
        
        camera.setPos(car_pos + world_offset)
        camera.setQuat(car_orientation)
    
    def update_hood_camera(self, camera):
        """Update hood camera on the car."""
        car_pos = self.physics.position
        car_orientation = self.physics.orientation
        
        # Camera position on hood
        local_offset = Vec3(0, 1.5, 0.5)
        world_offset = car_orientation.xform(local_offset)
        
        camera.setPos(car_pos + world_offset)
        camera.setQuat(car_orientation)
    
    def get_speed_kmh(self):
        """Get car speed in km/h."""
        return self.physics.get_speed_kmh()
    
    def get_current_gear(self):
        """Get current gear."""
        return self.physics.engine.current_gear
    
    def get_engine_rpm(self):
        """Get engine RPM."""
        return self.physics.engine.current_rpm
    
    def update(self, dt):
        """Update car systems."""
        # Update physics
        self.physics.update(dt)
        
        # Update visual representation
        self.update_visual_from_physics()
        
        # Update lap timing
        self.lap_time += dt

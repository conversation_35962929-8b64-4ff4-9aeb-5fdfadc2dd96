"""
Input Management System
======================

Advanced input handling for steering wheels, controllers, and keyboard.
"""

import math
from panda3d.core import *
from direct.showbase.DirectObject import DirectObject
from utils.math_utils import MathUtils

class InputManager(DirectObject):
    """Advanced input management for racing controls."""
    
    def __init__(self, game_engine):
        """Initialize input manager."""
        DirectObject.__init__(self)
        self.game_engine = game_engine
        
        # Input devices
        self.keyboard_enabled = True
        self.gamepad_enabled = True
        self.wheel_enabled = True
        
        # Current input values
        self.throttle = 0.0
        self.brake = 0.0
        self.steering = 0.0
        self.clutch = 0.0
        self.handbrake = 0.0
        
        # Input smoothing
        self.steering_smoothing = 0.1
        self.throttle_smoothing = 0.05
        self.brake_smoothing = 0.05
        
        # Keyboard state
        self.keys_pressed = set()
        
        # Gamepad/wheel settings
        self.steering_deadzone = 0.05
        self.throttle_deadzone = 0.02
        self.brake_deadzone = 0.02
        
        # Force feedback (for steering wheels)
        self.force_feedback_enabled = True
        self.force_feedback_strength = 1.0
        
        # Input mapping
        self.setup_input_mapping()
        
        # Setup input devices
        self.setup_keyboard()
        self.setup_gamepad()
        
        print("Input manager initialized")
    
    def setup_input_mapping(self):
        """Setup input device mappings."""
        # Keyboard mapping
        self.keyboard_map = {
            'throttle': ['w', 'arrow_up'],
            'brake': ['s', 'arrow_down'],
            'steer_left': ['a', 'arrow_left'],
            'steer_right': ['d', 'arrow_right'],
            'clutch': ['space'],
            'handbrake': ['x'],
            'gear_up': ['e'],
            'gear_down': ['q'],
            'camera_change': ['c'],
            'reset_car': ['r']
        }
        
        # Gamepad mapping (Xbox controller layout)
        self.gamepad_map = {
            'throttle': 'right_trigger',
            'brake': 'left_trigger',
            'steering': 'left_stick_x',
            'clutch': 'left_bumper',
            'handbrake': 'right_bumper',
            'gear_up': 'b',
            'gear_down': 'x',
            'camera_change': 'y'
        }
        
        # Steering wheel mapping
        self.wheel_map = {
            'steering': 'wheel_axis',
            'throttle': 'throttle_pedal',
            'brake': 'brake_pedal',
            'clutch': 'clutch_pedal',
            'gear_up': 'paddle_right',
            'gear_down': 'paddle_left'
        }
    
    def setup_keyboard(self):
        """Setup keyboard input handlers."""
        if not self.keyboard_enabled:
            return
        
        # Key press events
        for action, keys in self.keyboard_map.items():
            for key in keys:
                self.accept(key, self.on_key_press, [key])
                self.accept(f"{key}-up", self.on_key_release, [key])
    
    def setup_gamepad(self):
        """Setup gamepad input."""
        if not self.gamepad_enabled:
            return
        
        # Check for connected gamepads
        devices = self.game_engine.devices
        if devices:
            for device in devices.getDevices():
                if device.getDeviceClass() == InputDevice.DeviceClass.gamepad:
                    print(f"Found gamepad: {device.getName()}")
                    self.gamepad = device
                    break
    
    def on_key_press(self, key):
        """Handle key press events."""
        self.keys_pressed.add(key)
        
        # Handle discrete actions
        if key in self.keyboard_map.get('gear_up', []):
            self.shift_gear_up()
        elif key in self.keyboard_map.get('gear_down', []):
            self.shift_gear_down()
        elif key in self.keyboard_map.get('camera_change', []):
            self.change_camera_mode()
        elif key in self.keyboard_map.get('reset_car', []):
            self.reset_car()
    
    def on_key_release(self, key):
        """Handle key release events."""
        self.keys_pressed.discard(key)
    
    def update_keyboard_input(self):
        """Update continuous keyboard input."""
        if not self.keyboard_enabled:
            return
        
        # Throttle
        throttle_keys = self.keyboard_map.get('throttle', [])
        if any(key in self.keys_pressed for key in throttle_keys):
            target_throttle = 1.0
        else:
            target_throttle = 0.0
        
        # Brake
        brake_keys = self.keyboard_map.get('brake', [])
        if any(key in self.keys_pressed for key in brake_keys):
            target_brake = 1.0
        else:
            target_brake = 0.0
        
        # Steering
        left_keys = self.keyboard_map.get('steer_left', [])
        right_keys = self.keyboard_map.get('steer_right', [])
        
        target_steering = 0.0
        if any(key in self.keys_pressed for key in left_keys):
            target_steering -= 1.0
        if any(key in self.keys_pressed for key in right_keys):
            target_steering += 1.0
        
        # Clutch
        clutch_keys = self.keyboard_map.get('clutch', [])
        if any(key in self.keys_pressed for key in clutch_keys):
            target_clutch = 1.0
        else:
            target_clutch = 0.0
        
        # Handbrake
        handbrake_keys = self.keyboard_map.get('handbrake', [])
        if any(key in self.keys_pressed for key in handbrake_keys):
            target_handbrake = 1.0
        else:
            target_handbrake = 0.0
        
        # Apply smoothing
        self.throttle = MathUtils.lerp(self.throttle, target_throttle, self.throttle_smoothing)
        self.brake = MathUtils.lerp(self.brake, target_brake, self.brake_smoothing)
        self.steering = MathUtils.lerp(self.steering, target_steering, self.steering_smoothing)
        self.clutch = target_clutch  # No smoothing for clutch
        self.handbrake = target_handbrake
    
    def update_gamepad_input(self):
        """Update gamepad input."""
        if not self.gamepad_enabled or not hasattr(self, 'gamepad'):
            return
        
        # Read analog inputs
        if self.gamepad.hasAxis(InputDevice.Axis.right_trigger):
            raw_throttle = self.gamepad.getAxis(InputDevice.Axis.right_trigger).getValue()
            self.throttle = self.apply_deadzone(raw_throttle, self.throttle_deadzone)
        
        if self.gamepad.hasAxis(InputDevice.Axis.left_trigger):
            raw_brake = self.gamepad.getAxis(InputDevice.Axis.left_trigger).getValue()
            self.brake = self.apply_deadzone(raw_brake, self.brake_deadzone)
        
        if self.gamepad.hasAxis(InputDevice.Axis.left_x):
            raw_steering = self.gamepad.getAxis(InputDevice.Axis.left_x).getValue()
            target_steering = self.apply_deadzone(raw_steering, self.steering_deadzone)
            self.steering = MathUtils.lerp(self.steering, target_steering, self.steering_smoothing)
        
        # Read button inputs
        if self.gamepad.hasButton(InputDevice.Button.lshoulder):
            self.clutch = 1.0 if self.gamepad.getButton(InputDevice.Button.lshoulder).pressed else 0.0
        
        if self.gamepad.hasButton(InputDevice.Button.rshoulder):
            self.handbrake = 1.0 if self.gamepad.getButton(InputDevice.Button.rshoulder).pressed else 0.0
    
    def update_wheel_input(self):
        """Update steering wheel input."""
        if not self.wheel_enabled:
            return
        
        # This would interface with steering wheel hardware
        # Implementation depends on specific wheel hardware and drivers
        pass
    
    def apply_deadzone(self, value, deadzone):
        """Apply deadzone to analog input."""
        if abs(value) < deadzone:
            return 0.0
        
        # Scale the remaining range
        if value > 0:
            return (value - deadzone) / (1.0 - deadzone)
        else:
            return (value + deadzone) / (1.0 - deadzone)
    
    def shift_gear_up(self):
        """Shift to higher gear."""
        if hasattr(self.game_engine, 'car'):
            engine = self.game_engine.car.physics.engine
            current_gear = engine.current_gear
            max_gear = len(engine.gear_ratios)
            
            if current_gear < max_gear:
                engine.shift_gear(current_gear + 1)
    
    def shift_gear_down(self):
        """Shift to lower gear."""
        if hasattr(self.game_engine, 'car'):
            engine = self.game_engine.car.physics.engine
            current_gear = engine.current_gear
            
            if current_gear > 1:
                engine.shift_gear(current_gear - 1)
    
    def change_camera_mode(self):
        """Change camera mode."""
        if hasattr(self.game_engine, 'car'):
            car = self.game_engine.car
            modes = ["chase", "cockpit", "hood", "wheel"]
            current_index = modes.index(car.camera_mode)
            next_index = (current_index + 1) % len(modes)
            car.camera_mode = modes[next_index]
            print(f"Camera mode: {car.camera_mode}")
    
    def reset_car(self):
        """Reset car to upright position."""
        if hasattr(self.game_engine, 'car'):
            car = self.game_engine.car
            # Reset position and orientation
            car.physics.position = Vec3(0, 0, 5)
            car.physics.velocity = Vec3(0, 0, 0)
            car.physics.angular_velocity = Vec3(0, 0, 0)
            car.physics.orientation = Quat()
            print("Car reset")
    
    def calculate_force_feedback(self):
        """Calculate force feedback for steering wheel."""
        if not self.force_feedback_enabled or not hasattr(self.game_engine, 'car'):
            return 0.0
        
        car = self.game_engine.car
        physics = car.physics
        
        # Calculate forces that should be felt through steering
        # This is a simplified implementation
        
        # Tire forces on front wheels
        front_tire_forces = 0.0  # Would calculate from tire physics
        
        # Self-aligning torque
        speed_factor = min(1.0, physics.velocity.length() / 30.0)  # Scale with speed
        aligning_torque = -self.steering * speed_factor * 0.5
        
        # Road surface effects
        surface_feedback = 0.0  # Would add road texture effects
        
        total_feedback = (front_tire_forces + aligning_torque + surface_feedback) * self.force_feedback_strength
        
        return MathUtils.clamp(total_feedback, -1.0, 1.0)
    
    def get_input_state(self):
        """Get current input state."""
        return {
            'throttle': self.throttle,
            'brake': self.brake,
            'steering': self.steering,
            'clutch': self.clutch,
            'handbrake': self.handbrake
        }
    
    def set_input_sensitivity(self, steering=None, throttle=None, brake=None):
        """Adjust input sensitivity."""
        if steering is not None:
            self.steering_smoothing = MathUtils.clamp(steering, 0.01, 1.0)
        if throttle is not None:
            self.throttle_smoothing = MathUtils.clamp(throttle, 0.01, 1.0)
        if brake is not None:
            self.brake_smoothing = MathUtils.clamp(brake, 0.01, 1.0)
    
    def update(self, dt):
        """Update input system."""
        # Update all input sources
        self.update_keyboard_input()
        self.update_gamepad_input()
        self.update_wheel_input()
        
        # Apply inputs to car
        if hasattr(self.game_engine, 'car'):
            self.game_engine.car.set_control_inputs(
                self.throttle, self.brake, self.steering, self.clutch
            )
        
        # Calculate force feedback
        if self.force_feedback_enabled:
            feedback = self.calculate_force_feedback()
            # Would send feedback to steering wheel hardware

#!/usr/bin/env python3
"""
Setup script for Advanced Car Racing Simulation
"""

import sys
import subprocess
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    return True

def install_dependencies():
    """Install required dependencies."""
    print("Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        return False

def verify_installation():
    """Verify that all required modules can be imported."""
    print("Verifying installation...")
    
    required_modules = [
        'panda3d',
        'numpy',
        'scipy',
        'matplotlib',
        'pygame',
        'noise',
        'PIL'
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError:
            print(f"✗ {module}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\nFailed to import: {', '.join(failed_imports)}")
        return False
    
    print("\nAll modules imported successfully!")
    return True

def create_config_file():
    """Create default configuration file."""
    config_content = """# Advanced Racing Simulation Configuration

[Graphics]
resolution_width = 1920
resolution_height = 1080
fullscreen = false
vsync = true
antialiasing = true
shadow_quality = high

[Physics]
update_rate = 60
substeps = 10
tire_model = pacejka
suspension_model = advanced

[Input]
keyboard_enabled = true
gamepad_enabled = true
steering_wheel_enabled = true
force_feedback = true

[Telemetry]
logging_enabled = true
sample_rate = 60
auto_export = false

[Performance]
adaptive_quality = true
target_fps = 60
performance_monitoring = true
"""
    
    try:
        with open('config.ini', 'w') as f:
            f.write(config_content)
        print("Configuration file created: config.ini")
        return True
    except Exception as e:
        print(f"Error creating config file: {e}")
        return False

def run_tests():
    """Run basic system tests."""
    print("Running system tests...")
    
    # Test 1: Import all modules
    try:
        from physics.car_physics import CarPhysics
        from world.terrain_generator import TerrainGenerator
        from vehicle.car import Car
        print("✓ Module imports successful")
    except Exception as e:
        print(f"✗ Module import failed: {e}")
        return False
    
    # Test 2: Basic physics initialization
    try:
        physics = CarPhysics()
        print("✓ Physics system initialization successful")
    except Exception as e:
        print(f"✗ Physics initialization failed: {e}")
        return False
    
    print("All tests passed!")
    return True

def main():
    """Main setup function."""
    print("=" * 60)
    print("Advanced Car Racing Simulation - Setup")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        print("Setup failed during dependency installation.")
        return 1
    
    # Verify installation
    if not verify_installation():
        print("Setup failed during verification.")
        return 1
    
    # Create configuration file
    if not create_config_file():
        print("Warning: Could not create configuration file.")
    
    # Run tests
    if not run_tests():
        print("Setup completed with warnings (tests failed).")
        return 1
    
    print("\n" + "=" * 60)
    print("Setup completed successfully!")
    print("=" * 60)
    print("\nTo start the simulation, run:")
    print("  python main.py")
    print("\nFor help and documentation, see README.md")
    print("\nControls:")
    print("  W/S or ↑/↓  - Throttle/Brake")
    print("  A/D or ←/→  - Steering")
    print("  Q/E         - Gear Down/Up")
    print("  Space       - Clutch")
    print("  C           - Change Camera")
    print("  R           - Reset Car")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

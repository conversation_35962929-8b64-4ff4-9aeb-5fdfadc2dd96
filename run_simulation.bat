@echo off
echo Advanced Car Racing Simulation
echo ==============================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

REM Check if dependencies are installed
python -c "import panda3d" >nul 2>&1
if errorlevel 1 (
    echo Dependencies not found. Running setup...
    python setup.py
    if errorlevel 1 (
        echo Setup failed. Please check the error messages above.
        pause
        exit /b 1
    )
)

REM Launch the simulation
echo Starting simulation...
python main.py

REM Keep window open if there was an error
if errorlevel 1 (
    echo.
    echo Simulation exited with an error.
    pause
)

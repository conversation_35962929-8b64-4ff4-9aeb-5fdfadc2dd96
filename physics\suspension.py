"""
Advanced Suspension System
==========================

Realistic suspension dynamics with springs, dampers, and anti-roll bars.
"""

import math
import numpy as np
from panda3d.core import Vec3
from utils.math_utils import MathUtils

class SuspensionSystem:
    """Advanced suspension simulation."""
    
    def __init__(self, car_physics):
        """Initialize suspension system."""
        self.car_physics = car_physics
        
        # Suspension geometry
        self.wheel_travel = 0.15  # meters (total travel)
        self.static_compression = 0.05  # meters (static ride height compression)
        
        # Spring parameters (per wheel)
        self.spring_rate = 35000  # N/m (35 kN/m)
        self.spring_preload = self.spring_rate * self.static_compression
        
        # Damper parameters
        self.damping_compression = 3500  # N⋅s/m
        self.damping_rebound = 4200     # N⋅s/m (typically higher than compression)
        
        # Anti-roll bar parameters
        self.front_arb_rate = 15000  # N⋅m/rad
        self.rear_arb_rate = 12000   # N⋅m/rad
        
        # Suspension state for each wheel [FL, FR, RL, RR]
        self.wheel_displacements = [0.0, 0.0, 0.0, 0.0]  # Current compression
        self.wheel_velocities = [0.0, 0.0, 0.0, 0.0]     # Compression velocity
        self.previous_displacements = [0.0, 0.0, 0.0, 0.0]
        
        # Bump stops and droop limits
        self.bump_stop_position = self.wheel_travel * 0.9  # 90% of travel
        self.droop_limit = -self.wheel_travel * 0.3        # 30% extension
        self.bump_stop_rate = 200000  # Very stiff spring for bump stops
        
        # Progressive spring rates
        self.progressive_factor = 1.2  # Rate increases by this factor at full compression
        
        print("Suspension system initialized")
    
    def calculate_spring_force(self, displacement, wheel_index):
        """
        Calculate spring force with progressive rate.
        
        Args:
            displacement: Suspension compression (positive = compressed)
            wheel_index: Index of the wheel (0-3)
        
        Returns:
            Spring force (positive = upward)
        """
        # Base spring force
        base_force = self.spring_rate * (displacement + self.static_compression)
        
        # Progressive rate calculation
        compression_ratio = displacement / self.wheel_travel
        if compression_ratio > 0.7:  # Progressive rate kicks in at 70% compression
            progressive_multiplier = 1.0 + (compression_ratio - 0.7) * (self.progressive_factor - 1.0) / 0.3
            base_force *= progressive_multiplier
        
        # Bump stop
        if displacement > self.bump_stop_position:
            bump_compression = displacement - self.bump_stop_position
            bump_force = self.bump_stop_rate * bump_compression**2  # Non-linear bump stop
            base_force += bump_force
        
        # Droop limit (tension in spring)
        elif displacement < self.droop_limit:
            droop_extension = abs(displacement - self.droop_limit)
            droop_force = self.spring_rate * droop_extension * 0.1  # Weak tension
            base_force = -droop_force
        
        return base_force
    
    def calculate_damper_force(self, velocity, wheel_index):
        """
        Calculate damper force based on compression/rebound velocity.
        
        Args:
            velocity: Suspension velocity (positive = compressing)
            wheel_index: Index of the wheel (0-3)
        
        Returns:
            Damper force (positive = upward)
        """
        if velocity > 0:  # Compression
            damper_force = -self.damping_compression * velocity
        else:  # Rebound
            damper_force = -self.damping_rebound * velocity
        
        # Non-linear damping at high velocities
        velocity_factor = abs(velocity)
        if velocity_factor > 1.0:  # m/s
            non_linear_factor = 1.0 + (velocity_factor - 1.0) * 0.5
            damper_force *= non_linear_factor
        
        return damper_force
    
    def calculate_anti_roll_bar_forces(self):
        """Calculate anti-roll bar forces."""
        # Front anti-roll bar
        front_roll_angle = self.wheel_displacements[0] - self.wheel_displacements[1]
        front_arb_torque = self.front_arb_rate * front_roll_angle
        front_arb_force = front_arb_torque / (self.car_physics.track_width / 2)
        
        # Rear anti-roll bar
        rear_roll_angle = self.wheel_displacements[2] - self.wheel_displacements[3]
        rear_arb_torque = self.rear_arb_rate * rear_roll_angle
        rear_arb_force = rear_arb_torque / (self.car_physics.track_width / 2)
        
        # Forces on each wheel (positive = upward)
        arb_forces = [
            -front_arb_force / 2,  # FL
            front_arb_force / 2,   # FR
            -rear_arb_force / 2,   # RL
            rear_arb_force / 2     # RR
        ]
        
        return arb_forces
    
    def calculate_suspension_forces(self):
        """Calculate total suspension forces for all wheels."""
        suspension_forces = []
        arb_forces = self.calculate_anti_roll_bar_forces()
        
        for i in range(4):
            # Spring force
            spring_force = self.calculate_spring_force(self.wheel_displacements[i], i)
            
            # Damper force
            damper_force = self.calculate_damper_force(self.wheel_velocities[i], i)
            
            # Anti-roll bar force
            arb_force = arb_forces[i]
            
            # Total suspension force
            total_force = spring_force + damper_force + arb_force
            
            # Force vector (vertical only for now)
            force_vector = Vec3(0, 0, total_force)
            suspension_forces.append(force_vector)
        
        return suspension_forces
    
    def update_wheel_displacements(self, dt):
        """Update wheel displacements based on vehicle motion."""
        # This is a simplified implementation
        # In reality, this would involve ray casting to find ground contact points
        
        # Store previous displacements
        self.previous_displacements = self.wheel_displacements.copy()
        
        # Calculate wheel positions in world space
        for i in range(4):
            wheel_world_pos = (self.car_physics.position + 
                             self.car_physics.orientation.xform(self.car_physics.wheel_positions[i]))
            
            # Simplified ground height calculation (flat ground at z=0)
            ground_height = 0.0
            wheel_height = wheel_world_pos.z
            
            # Calculate compression (positive = compressed)
            target_compression = max(0, ground_height + self.car_physics.tire_physics.tire_radius - wheel_height)
            
            # Limit compression to wheel travel
            target_compression = MathUtils.clamp(target_compression, 
                                               self.droop_limit, 
                                               self.wheel_travel)
            
            # Update displacement with some damping for stability
            displacement_change = target_compression - self.wheel_displacements[i]
            self.wheel_displacements[i] += displacement_change * 0.1  # Smooth transition
        
        # Calculate velocities
        if dt > 0:
            for i in range(4):
                self.wheel_velocities[i] = ((self.wheel_displacements[i] - self.previous_displacements[i]) / dt)
    
    def get_ride_height(self):
        """Get average ride height."""
        avg_compression = sum(self.wheel_displacements) / 4
        return self.static_compression - avg_compression
    
    def get_roll_angle(self):
        """Get vehicle roll angle in radians."""
        left_compression = (self.wheel_displacements[0] + self.wheel_displacements[2]) / 2
        right_compression = (self.wheel_displacements[1] + self.wheel_displacements[3]) / 2
        
        compression_diff = right_compression - left_compression
        roll_angle = math.atan2(compression_diff, self.car_physics.track_width)
        
        return roll_angle
    
    def get_pitch_angle(self):
        """Get vehicle pitch angle in radians."""
        front_compression = (self.wheel_displacements[0] + self.wheel_displacements[1]) / 2
        rear_compression = (self.wheel_displacements[2] + self.wheel_displacements[3]) / 2
        
        compression_diff = front_compression - rear_compression
        pitch_angle = math.atan2(compression_diff, self.car_physics.wheelbase)
        
        return pitch_angle
    
    def adjust_suspension_settings(self, spring_rate_mult=1.0, damping_mult=1.0, arb_mult=1.0):
        """Adjust suspension settings for tuning."""
        self.spring_rate *= spring_rate_mult
        self.damping_compression *= damping_mult
        self.damping_rebound *= damping_mult
        self.front_arb_rate *= arb_mult
        self.rear_arb_rate *= arb_mult
        
        print(f"Suspension adjusted: Spring {spring_rate_mult:.2f}x, "
              f"Damping {damping_mult:.2f}x, ARB {arb_mult:.2f}x")
    
    def get_suspension_telemetry(self):
        """Get suspension telemetry data."""
        return {
            'wheel_displacements': self.wheel_displacements.copy(),
            'wheel_velocities': self.wheel_velocities.copy(),
            'ride_height': self.get_ride_height(),
            'roll_angle': math.degrees(self.get_roll_angle()),
            'pitch_angle': math.degrees(self.get_pitch_angle()),
            'spring_forces': [self.calculate_spring_force(d, i) for i, d in enumerate(self.wheel_displacements)],
            'damper_forces': [self.calculate_damper_force(v, i) for i, v in enumerate(self.wheel_velocities)]
        }
    
    def update(self, dt):
        """Update suspension system."""
        # Update wheel displacements
        self.update_wheel_displacements(dt)
        
        # Calculate and apply suspension forces
        suspension_forces = self.calculate_suspension_forces()
        
        # Apply forces to car physics (this would be integrated with the main physics loop)
        # For now, we'll store them for use by the car physics system
        self.current_forces = suspension_forces
        
        return suspension_forces

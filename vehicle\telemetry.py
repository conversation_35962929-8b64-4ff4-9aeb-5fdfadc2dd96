"""
Telemetry System
===============

Comprehensive data logging and analysis for the racing simulation.
"""

import time
import json
import csv
import numpy as np
from collections import deque
import matplotlib.pyplot as plt
from panda3d.core import Vec3

class TelemetrySystem:
    """Advanced telemetry and data logging system."""
    
    def __init__(self, game_engine):
        """Initialize telemetry system."""
        self.game_engine = game_engine
        
        # Data storage
        self.data_buffer = deque(maxlen=10000)  # Store last 10000 samples
        self.session_data = []
        
        # Sampling rate
        self.sample_rate = 60  # Hz
        self.sample_interval = 1.0 / self.sample_rate
        self.last_sample_time = 0.0
        
        # Data categories
        self.data_categories = {
            'vehicle': ['speed', 'acceleration', 'position', 'orientation'],
            'engine': ['rpm', 'torque', 'power', 'gear', 'temperature'],
            'tires': ['temperature', 'wear', 'slip_angle', 'slip_ratio'],
            'suspension': ['compression', 'velocity', 'forces'],
            'aerodynamics': ['downforce', 'drag', 'side_force'],
            'environment': ['weather', 'temperature', 'wind'],
            'input': ['throttle', 'brake', 'steering', 'clutch']
        }
        
        # Performance metrics
        self.lap_data = []
        self.sector_times = []
        self.best_times = {
            'lap': float('inf'),
            'sector1': float('inf'),
            'sector2': float('inf'),
            'sector3': float('inf')
        }
        
        # Real-time analysis
        self.analysis_enabled = True
        self.alerts = []
        
        print("Telemetry system initialized")
    
    def collect_data_sample(self):
        """Collect a complete data sample."""
        current_time = time.time()
        
        sample = {
            'timestamp': current_time,
            'game_time': globalClock.getFrameTime(),
            'vehicle': self.collect_vehicle_data(),
            'engine': self.collect_engine_data(),
            'tires': self.collect_tire_data(),
            'suspension': self.collect_suspension_data(),
            'aerodynamics': self.collect_aerodynamics_data(),
            'environment': self.collect_environment_data(),
            'input': self.collect_input_data()
        }
        
        return sample
    
    def collect_vehicle_data(self):
        """Collect vehicle dynamics data."""
        if not hasattr(self.game_engine, 'car'):
            return {}
        
        car = self.game_engine.car
        physics = car.physics
        
        return {
            'speed_kmh': physics.get_speed_kmh(),
            'speed_ms': physics.velocity.length(),
            'velocity': [physics.velocity.x, physics.velocity.y, physics.velocity.z],
            'position': [physics.position.x, physics.position.y, physics.position.z],
            'orientation': [physics.orientation.getI(), physics.orientation.getJ(), 
                          physics.orientation.getK(), physics.orientation.getR()],
            'angular_velocity': [physics.angular_velocity.x, physics.angular_velocity.y, 
                               physics.angular_velocity.z],
            'local_velocity': [physics.get_local_velocity().x, physics.get_local_velocity().y, 
                             physics.get_local_velocity().z]
        }
    
    def collect_engine_data(self):
        """Collect engine telemetry data."""
        if not hasattr(self.game_engine, 'car'):
            return {}
        
        engine = self.game_engine.car.physics.engine
        telemetry = engine.get_engine_telemetry()
        
        return telemetry
    
    def collect_tire_data(self):
        """Collect tire telemetry data."""
        if not hasattr(self.game_engine, 'car'):
            return {}
        
        tire_physics = self.game_engine.car.physics.tire_physics
        tire_info = tire_physics.get_tire_info()
        
        # Collect data for all four tires
        tire_data = {
            'front_left': tire_info.copy(),
            'front_right': tire_info.copy(),
            'rear_left': tire_info.copy(),
            'rear_right': tire_info.copy()
        }
        
        return tire_data
    
    def collect_suspension_data(self):
        """Collect suspension telemetry data."""
        if not hasattr(self.game_engine, 'car'):
            return {}
        
        suspension = self.game_engine.car.physics.suspension
        telemetry = suspension.get_suspension_telemetry()
        
        return telemetry
    
    def collect_aerodynamics_data(self):
        """Collect aerodynamics telemetry data."""
        if not hasattr(self.game_engine, 'car'):
            return {}
        
        aero = self.game_engine.car.physics.aerodynamics
        velocity = self.game_engine.car.physics.velocity
        telemetry = aero.get_aerodynamic_telemetry(velocity)
        
        return telemetry
    
    def collect_environment_data(self):
        """Collect environmental data."""
        if not hasattr(self.game_engine, 'environment'):
            return {}
        
        env_data = self.game_engine.environment.get_environmental_data()
        return env_data
    
    def collect_input_data(self):
        """Collect control input data."""
        if not hasattr(self.game_engine, 'car'):
            return {}
        
        physics = self.game_engine.car.physics
        
        return {
            'throttle': physics.throttle,
            'brake': physics.brake,
            'steering': physics.steering,
            'clutch': physics.clutch
        }
    
    def analyze_data_sample(self, sample):
        """Perform real-time analysis on data sample."""
        if not self.analysis_enabled:
            return
        
        # Check for performance alerts
        self.check_performance_alerts(sample)
        
        # Update performance metrics
        self.update_performance_metrics(sample)
    
    def check_performance_alerts(self, sample):
        """Check for performance issues and generate alerts."""
        alerts = []
        
        # Engine temperature alert
        if 'engine' in sample and 'engine_temp' in sample['engine']:
            temp = sample['engine']['engine_temp']
            if temp > 110:
                alerts.append({
                    'type': 'warning',
                    'category': 'engine',
                    'message': f'Engine temperature high: {temp:.1f}°C',
                    'timestamp': sample['timestamp']
                })
        
        # Tire temperature alert
        if 'tires' in sample:
            for tire_pos, tire_data in sample['tires'].items():
                if 'temperature' in tire_data:
                    temp = tire_data['temperature']
                    if temp > 120:
                        alerts.append({
                            'type': 'warning',
                            'category': 'tires',
                            'message': f'{tire_pos} tire overheating: {temp:.1f}°C',
                            'timestamp': sample['timestamp']
                        })
        
        # Speed alert (if going too fast for conditions)
        if 'vehicle' in sample and 'environment' in sample:
            speed = sample['vehicle']['speed_kmh']
            visibility = sample['environment'].get('visibility', 10000)
            
            if speed > 200 and visibility < 100:
                alerts.append({
                    'type': 'danger',
                    'category': 'safety',
                    'message': f'High speed in low visibility: {speed:.1f} km/h',
                    'timestamp': sample['timestamp']
                })
        
        # Add alerts to queue
        self.alerts.extend(alerts)
        
        # Keep only recent alerts
        current_time = sample['timestamp']
        self.alerts = [alert for alert in self.alerts 
                      if current_time - alert['timestamp'] < 30.0]  # 30 seconds
    
    def update_performance_metrics(self, sample):
        """Update performance metrics."""
        # This would update lap times, sector times, etc.
        # Simplified implementation
        pass
    
    def save_session_data(self, filename=None):
        """Save session data to file."""
        if not filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"telemetry_session_{timestamp}.json"
        
        session_summary = {
            'session_info': {
                'start_time': self.session_data[0]['timestamp'] if self.session_data else 0,
                'end_time': self.session_data[-1]['timestamp'] if self.session_data else 0,
                'duration': len(self.session_data) / self.sample_rate,
                'samples': len(self.session_data)
            },
            'best_times': self.best_times,
            'data': list(self.session_data)
        }
        
        try:
            with open(filename, 'w') as f:
                json.dump(session_summary, f, indent=2)
            print(f"Telemetry data saved to {filename}")
        except Exception as e:
            print(f"Error saving telemetry data: {e}")
    
    def export_to_csv(self, filename=None, category='vehicle'):
        """Export specific data category to CSV."""
        if not filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"telemetry_{category}_{timestamp}.csv"
        
        if not self.session_data:
            print("No data to export")
            return
        
        # Extract data for specified category
        rows = []
        headers = ['timestamp']
        
        for sample in self.session_data:
            if category in sample:
                row = [sample['timestamp']]
                category_data = sample[category]
                
                if not headers[1:]:  # First time, set headers
                    headers.extend(self.flatten_dict_keys(category_data))
                
                row.extend(self.flatten_dict_values(category_data))
                rows.append(row)
        
        try:
            with open(filename, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(headers)
                writer.writerows(rows)
            print(f"Data exported to {filename}")
        except Exception as e:
            print(f"Error exporting data: {e}")
    
    def flatten_dict_keys(self, d, parent_key='', sep='_'):
        """Flatten nested dictionary keys."""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self.flatten_dict_keys(v, new_key, sep=sep))
            else:
                items.append(new_key)
        return items
    
    def flatten_dict_values(self, d):
        """Flatten nested dictionary values."""
        items = []
        for v in d.values():
            if isinstance(v, dict):
                items.extend(self.flatten_dict_values(v))
            elif isinstance(v, list):
                items.extend(v)
            else:
                items.append(v)
        return items
    
    def create_performance_graph(self, metric='speed_kmh', duration=60):
        """Create real-time performance graph."""
        try:
            # Get recent data
            recent_data = list(self.data_buffer)[-duration*self.sample_rate:]
            
            if not recent_data:
                return
            
            times = [sample['game_time'] for sample in recent_data]
            values = []
            
            for sample in recent_data:
                if 'vehicle' in sample and metric in sample['vehicle']:
                    values.append(sample['vehicle'][metric])
                else:
                    values.append(0)
            
            # Create plot
            plt.figure(figsize=(12, 6))
            plt.plot(times, values)
            plt.title(f'{metric} - Last {duration} seconds')
            plt.xlabel('Time (s)')
            plt.ylabel(metric)
            plt.grid(True)
            plt.show()
            
        except Exception as e:
            print(f"Error creating graph: {e}")
    
    def get_current_alerts(self):
        """Get current active alerts."""
        return self.alerts
    
    def get_performance_summary(self):
        """Get performance summary."""
        if not self.session_data:
            return {}
        
        # Calculate basic statistics
        speeds = [sample['vehicle']['speed_kmh'] for sample in self.session_data 
                 if 'vehicle' in sample and 'speed_kmh' in sample['vehicle']]
        
        if speeds:
            summary = {
                'max_speed': max(speeds),
                'avg_speed': sum(speeds) / len(speeds),
                'session_duration': len(self.session_data) / self.sample_rate,
                'samples_collected': len(self.session_data),
                'best_times': self.best_times,
                'active_alerts': len(self.alerts)
            }
        else:
            summary = {'error': 'No speed data available'}
        
        return summary
    
    def update(self, dt):
        """Update telemetry system."""
        current_time = time.time()
        
        # Check if it's time for a new sample
        if current_time - self.last_sample_time >= self.sample_interval:
            # Collect data sample
            sample = self.collect_data_sample()
            
            # Add to buffers
            self.data_buffer.append(sample)
            self.session_data.append(sample)
            
            # Analyze sample
            self.analyze_data_sample(sample)
            
            self.last_sample_time = current_time

"""
Procedural Terrain Generation System
====================================

Advanced terrain generation with realistic racing environments.
"""

import numpy as np
import math
from panda3d.core import *
from panda3d.bullet import *
from direct.task import Task
# import noise  # Temporarily disabled due to build issues
import random

class TerrainGenerator:
    """Procedural terrain generation for racing environments."""
    
    def __init__(self, game_engine):
        """Initialize terrain generator."""
        self.game_engine = game_engine
        
        # Terrain parameters
        self.terrain_size = 2000  # meters
        self.terrain_resolution = 512  # vertices per side
        self.height_scale = 50.0  # maximum height variation
        
        # Noise parameters for terrain generation
        self.noise_scale = 0.005
        self.noise_octaves = 6
        self.noise_persistence = 0.5
        self.noise_lacunarity = 2.0
        
        # Track parameters
        self.track_width = 12.0  # meters
        self.track_banking_max = 15.0  # degrees
        self.elevation_change_max = 30.0  # meters
        
        # Surface types
        self.surface_types = {
            'asphalt': {'friction': 1.1, 'color': (0.2, 0.2, 0.2, 1.0)},
            'concrete': {'friction': 1.0, 'color': (0.6, 0.6, 0.6, 1.0)},
            'gravel': {'friction': 0.6, 'color': (0.5, 0.4, 0.3, 1.0)},
            'grass': {'friction': 0.4, 'color': (0.2, 0.6, 0.2, 1.0)},
            'dirt': {'friction': 0.5, 'color': (0.4, 0.3, 0.2, 1.0)}
        }
        
        print("Terrain generator initialized")
    
    def generate_height_map(self, size, resolution):
        """Generate height map using Perlin noise."""
        height_map = np.zeros((resolution, resolution))
        
        # Generate base terrain using multiple octaves of noise
        for i in range(resolution):
            for j in range(resolution):
                x = (i / resolution) * size
                y = (j / resolution) * size
                
                # Simple procedural noise (replacement for Perlin noise)
                height = 0
                amplitude = 1
                frequency = self.noise_scale

                for octave in range(self.noise_octaves):
                    # Simple sine-based noise as replacement
                    noise_val = (math.sin(x * frequency * 0.1) * math.cos(y * frequency * 0.1) +
                                math.sin(x * frequency * 0.07 + 1.5) * math.cos(y * frequency * 0.13 + 2.1) +
                                random.random() * 0.2 - 0.1)
                    height += noise_val * amplitude

                    amplitude *= self.noise_persistence
                    frequency *= self.noise_lacunarity
                
                height_map[i, j] = height * self.height_scale
        
        return height_map
    
    def smooth_terrain_for_tracks(self, height_map, track_paths):
        """Smooth terrain along track paths."""
        resolution = height_map.shape[0]
        smoothed_map = height_map.copy()
        
        for track_path in track_paths:
            for point in track_path:
                # Convert world coordinates to height map indices
                x_idx = int((point[0] + self.terrain_size/2) / self.terrain_size * resolution)
                y_idx = int((point[1] + self.terrain_size/2) / self.terrain_size * resolution)
                
                # Smooth area around track
                smooth_radius = int(self.track_width / self.terrain_size * resolution)
                
                for dx in range(-smooth_radius, smooth_radius + 1):
                    for dy in range(-smooth_radius, smooth_radius + 1):
                        nx, ny = x_idx + dx, y_idx + dy
                        if 0 <= nx < resolution and 0 <= ny < resolution:
                            distance = math.sqrt(dx*dx + dy*dy)
                            if distance <= smooth_radius:
                                # Smooth transition
                                weight = 1.0 - (distance / smooth_radius)
                                target_height = point[2]  # Track elevation
                                smoothed_map[nx, ny] = (
                                    smoothed_map[nx, ny] * (1 - weight) + 
                                    target_height * weight
                                )
        
        return smoothed_map
    
    def create_terrain_mesh(self, height_map):
        """Create 3D terrain mesh from height map."""
        resolution = height_map.shape[0]
        
        # Create vertex data
        vdata = GeomVertexData('terrain', GeomVertexFormat.getV3n3t2(), Geom.UHStatic)
        vdata.setNumRows(resolution * resolution)
        
        vertex = GeomVertexWriter(vdata, 'vertex')
        normal = GeomVertexWriter(vdata, 'normal')
        texcoord = GeomVertexWriter(vdata, 'texcoord')
        
        # Generate vertices
        for i in range(resolution):
            for j in range(resolution):
                x = (i / (resolution - 1)) * self.terrain_size - self.terrain_size/2
                y = (j / (resolution - 1)) * self.terrain_size - self.terrain_size/2
                z = height_map[i, j]
                
                vertex.addData3(x, y, z)
                
                # Calculate normal
                if i > 0 and i < resolution-1 and j > 0 and j < resolution-1:
                    # Use neighboring points to calculate normal
                    dx = height_map[i+1, j] - height_map[i-1, j]
                    dy = height_map[i, j+1] - height_map[i, j-1]
                    
                    normal_vec = Vec3(-dx, -dy, 2.0)
                    normal_vec.normalize()
                    normal.addData3(normal_vec)
                else:
                    normal.addData3(0, 0, 1)
                
                # Texture coordinates
                texcoord.addData2(i / (resolution - 1), j / (resolution - 1))

        # Create geometry
        geom = Geom(vdata)

        # Create triangles
        geom_triangles = GeomTriangles(Geom.UHStatic)
        
        for i in range(resolution - 1):
            for j in range(resolution - 1):
                # Two triangles per quad
                v1 = i * resolution + j
                v2 = i * resolution + (j + 1)
                v3 = (i + 1) * resolution + j
                v4 = (i + 1) * resolution + (j + 1)
                
                # First triangle
                geom_triangles.addVertices(v1, v2, v3)
                # Second triangle
                geom_triangles.addVertices(v2, v4, v3)
        
        geom_triangles.closePrimitive()
        geom.addPrimitive(geom_triangles)
        
        # Create geom node
        geom_node = GeomNode('terrain')
        geom_node.addGeom(geom)
        
        return geom_node
    
    def create_terrain_physics(self, height_map):
        """Create physics collision mesh for terrain."""
        resolution = height_map.shape[0]
        
        # Create triangle mesh for physics
        mesh = BulletTriangleMesh()
        
        for i in range(resolution - 1):
            for j in range(resolution - 1):
                # Get four corners of quad
                x1 = (i / (resolution - 1)) * self.terrain_size - self.terrain_size/2
                y1 = (j / (resolution - 1)) * self.terrain_size - self.terrain_size/2
                z1 = height_map[i, j]
                
                x2 = ((i + 1) / (resolution - 1)) * self.terrain_size - self.terrain_size/2
                y2 = (j / (resolution - 1)) * self.terrain_size - self.terrain_size/2
                z2 = height_map[i + 1, j]
                
                x3 = (i / (resolution - 1)) * self.terrain_size - self.terrain_size/2
                y3 = ((j + 1) / (resolution - 1)) * self.terrain_size - self.terrain_size/2
                z3 = height_map[i, j + 1]
                
                x4 = ((i + 1) / (resolution - 1)) * self.terrain_size - self.terrain_size/2
                y4 = ((j + 1) / (resolution - 1)) * self.terrain_size - self.terrain_size/2
                z4 = height_map[i + 1, j + 1]
                
                # Add two triangles
                mesh.addTriangle(
                    Point3(x1, y1, z1),
                    Point3(x2, y2, z2),
                    Point3(x3, y3, z3)
                )
                
                mesh.addTriangle(
                    Point3(x2, y2, z2),
                    Point3(x4, y4, z4),
                    Point3(x3, y3, z3)
                )
        
        # Create rigid body
        terrain_shape = BulletTriangleMeshShape(mesh, dynamic=False)
        terrain_body = BulletRigidBodyNode('terrain')
        terrain_body.addShape(terrain_shape)
        
        return terrain_body
    
    def generate_track_path(self, start_pos, length, complexity=0.5):
        """Generate a racing track path."""
        points = []
        current_pos = Vec3(start_pos)
        current_direction = 0.0  # radians
        
        segment_length = 50.0  # meters per segment
        num_segments = int(length / segment_length)
        
        for i in range(num_segments):
            points.append((current_pos.x, current_pos.y, current_pos.z))
            
            # Add some randomness to direction
            direction_change = (np.random.random() - 0.5) * complexity * 0.5
            current_direction += direction_change
            
            # Move to next point
            current_pos.x += segment_length * math.cos(current_direction)
            current_pos.y += segment_length * math.sin(current_direction)
            
            # Add elevation changes
            elevation_change = (np.random.random() - 0.5) * self.elevation_change_max * 0.1
            current_pos.z += elevation_change
        
        return points
    
    def generate_tracks(self, num_tracks=3):
        """Generate multiple interconnected racing tracks."""
        tracks = []
        
        for i in range(num_tracks):
            # Start positions spread around the terrain
            angle = (i / num_tracks) * 2 * math.pi
            start_x = math.cos(angle) * self.terrain_size * 0.3
            start_y = math.sin(angle) * self.terrain_size * 0.3
            start_pos = Vec3(start_x, start_y, 0)
            
            # Generate track
            track_length = 2000 + np.random.random() * 1000  # 2-3 km
            complexity = 0.3 + np.random.random() * 0.4  # Varying complexity
            
            track_path = self.generate_track_path(start_pos, track_length, complexity)
            tracks.append(track_path)
        
        return tracks
    
    def generate_terrain(self, size=None, detail=None):
        """Generate complete terrain with tracks."""
        if size:
            self.terrain_size = size
        if detail:
            self.terrain_resolution = detail
        
        print(f"Generating terrain: {self.terrain_size}m x {self.terrain_size}m")
        print(f"Resolution: {self.terrain_resolution} x {self.terrain_resolution}")
        
        # Generate base height map
        height_map = self.generate_height_map(self.terrain_size, self.terrain_resolution)
        
        # Generate tracks first
        tracks = self.generate_tracks()
        
        # Smooth terrain for tracks
        height_map = self.smooth_terrain_for_tracks(height_map, tracks)
        
        # Create visual mesh
        terrain_geom = self.create_terrain_mesh(height_map)
        terrain_np = self.game_engine.render.attachNewNode(terrain_geom)
        
        # Apply terrain texture
        self.apply_terrain_texture(terrain_np)
        
        # Create physics mesh
        terrain_physics = self.create_terrain_physics(height_map)
        terrain_physics_np = self.game_engine.render.attachNewNode(terrain_physics)
        self.game_engine.physics_world.attachRigidBody(terrain_physics)
        
        print("Terrain generation complete")
        
        return terrain_np
    
    def apply_terrain_texture(self, terrain_np):
        """Apply procedural textures to terrain."""
        # Create a simple texture for now
        # In a full implementation, this would use multiple textures based on height, slope, etc.
        
        # Set material properties
        material = Material()
        material.setShininess(1.0)
        material.setAmbient(VBase4(0.3, 0.3, 0.3, 1))
        material.setDiffuse(VBase4(0.5, 0.4, 0.3, 1))
        material.setSpecular(VBase4(0.1, 0.1, 0.1, 1))
        
        terrain_np.setMaterial(material)
        terrain_np.setTwoSided(False)

"""
Advanced Car Physics System
===========================

Comprehensive car physics simulation with realistic dynamics.
"""

import numpy as np
import math
from panda3d.core import Vec3, Quat
from panda3d.bullet import *

from utils.math_utils import MathUtils
from physics.tire_physics import TirePhysics
from physics.suspension import SuspensionSystem
from physics.aerodynamics import AerodynamicsSystem
from physics.engine import EngineSystem

class CarPhysics:
    """Advanced car physics simulation."""
    
    def __init__(self, mass=1400, wheelbase=2.7, track_width=1.5):
        """
        Initialize car physics.
        
        Args:
            mass: Vehicle mass in kg
            wheelbase: Distance between front and rear axles in meters
            track_width: Distance between left and right wheels in meters
        """
        # Vehicle parameters
        self.mass = mass
        self.wheelbase = wheelbase
        self.track_width = track_width
        
        # Center of gravity
        self.cg_height = 0.5  # meters above ground
        self.cg_position = Vec3(0, 0.3, self.cg_height)  # Slightly forward of center
        
        # Inertia tensor (simplified)
        self.inertia = self.calculate_inertia_tensor()
        
        # Vehicle state
        self.position = Vec3(0, 0, 0)
        self.velocity = Vec3(0, 0, 0)
        self.angular_velocity = Vec3(0, 0, 0)
        self.orientation = Quat()
        
        # Forces and torques
        self.total_force = Vec3(0, 0, 0)
        self.total_torque = Vec3(0, 0, 0)
        
        # Subsystems
        self.tire_physics = TirePhysics()
        self.suspension = SuspensionSystem(self)
        self.aerodynamics = AerodynamicsSystem(self)
        self.engine = EngineSystem(self)
        
        # Wheel positions relative to CG
        self.wheel_positions = [
            Vec3(-self.track_width/2, self.wheelbase/2, -self.cg_height),   # Front left
            Vec3(self.track_width/2, self.wheelbase/2, -self.cg_height),    # Front right
            Vec3(-self.track_width/2, -self.wheelbase/2, -self.cg_height),  # Rear left
            Vec3(self.track_width/2, -self.wheelbase/2, -self.cg_height)    # Rear right
        ]
        
        # Wheel states
        self.wheel_speeds = [0.0, 0.0, 0.0, 0.0]
        self.wheel_angles = [0.0, 0.0, 0.0, 0.0]  # Steering angles
        self.wheel_loads = [self.mass * 9.81 / 4] * 4  # Normal forces
        
        # Control inputs
        self.throttle = 0.0  # 0-1
        self.brake = 0.0     # 0-1
        self.steering = 0.0  # -1 to 1
        self.clutch = 0.0    # 0-1
        
        print("Car physics initialized")
    
    def calculate_inertia_tensor(self):
        """Calculate vehicle inertia tensor."""
        # Simplified inertia calculation for a box-shaped vehicle
        Ixx = (1/12) * self.mass * (self.track_width**2 + (self.cg_height*2)**2)
        Iyy = (1/12) * self.mass * (self.wheelbase**2 + (self.cg_height*2)**2)
        Izz = (1/12) * self.mass * (self.wheelbase**2 + self.track_width**2)
        
        return Vec3(Ixx, Iyy, Izz)
    
    def set_control_inputs(self, throttle, brake, steering, clutch=0.0):
        """Set control inputs for the vehicle."""
        self.throttle = MathUtils.clamp(throttle, 0.0, 1.0)
        self.brake = MathUtils.clamp(brake, 0.0, 1.0)
        self.steering = MathUtils.clamp(steering, -1.0, 1.0)
        self.clutch = MathUtils.clamp(clutch, 0.0, 1.0)
    
    def calculate_wheel_loads(self):
        """Calculate normal forces on each wheel considering weight transfer."""
        # Base static load
        static_load = self.mass * 9.81 / 4
        
        # Calculate accelerations in vehicle frame
        local_accel = self.get_local_acceleration()
        
        # Weight transfer calculations
        weight_transfer = MathUtils.calculate_weight_transfer(
            local_accel, self.mass, self.wheelbase, self.cg_height
        )
        
        # Distribute loads
        front_transfer = weight_transfer.y / 2
        rear_transfer = -weight_transfer.y / 2
        left_transfer = -weight_transfer.x / 2
        right_transfer = weight_transfer.x / 2
        
        self.wheel_loads[0] = static_load + front_transfer + left_transfer   # FL
        self.wheel_loads[1] = static_load + front_transfer + right_transfer  # FR
        self.wheel_loads[2] = static_load + rear_transfer + left_transfer    # RL
        self.wheel_loads[3] = static_load + rear_transfer + right_transfer   # RR
        
        # Ensure positive loads
        for i in range(4):
            self.wheel_loads[i] = max(0, self.wheel_loads[i])
    
    def get_local_acceleration(self):
        """Get acceleration in vehicle local coordinates."""
        # This would be calculated from previous frame's velocity change
        # For now, return zero (would be implemented with proper integration)
        return Vec3(0, 0, 0)
    
    def calculate_steering_angles(self):
        """Calculate individual wheel steering angles using Ackermann geometry."""
        max_steering_angle = math.radians(30)  # 30 degrees max
        steering_angle = self.steering * max_steering_angle
        
        if abs(steering_angle) < 0.001:
            # Straight ahead
            self.wheel_angles = [0.0, 0.0, 0.0, 0.0]
        else:
            # Ackermann steering geometry
            turning_radius = self.wheelbase / math.tan(abs(steering_angle))
            
            # Inner and outer wheel angles
            if steering_angle > 0:  # Left turn
                inner_angle = math.atan(self.wheelbase / (turning_radius - self.track_width/2))
                outer_angle = math.atan(self.wheelbase / (turning_radius + self.track_width/2))
                self.wheel_angles[0] = inner_angle   # FL
                self.wheel_angles[1] = outer_angle   # FR
            else:  # Right turn
                inner_angle = math.atan(self.wheelbase / (turning_radius - self.track_width/2))
                outer_angle = math.atan(self.wheelbase / (turning_radius + self.track_width/2))
                self.wheel_angles[0] = -outer_angle  # FL
                self.wheel_angles[1] = -inner_angle  # FR
            
            # Rear wheels don't steer
            self.wheel_angles[2] = 0.0  # RL
            self.wheel_angles[3] = 0.0  # RR
    
    def calculate_tire_forces(self):
        """Calculate forces from all tires."""
        tire_forces = []
        
        for i in range(4):
            # Get wheel velocity in world coordinates
            wheel_world_pos = self.position + self.orientation.xform(self.wheel_positions[i])
            wheel_velocity = self.velocity + self.angular_velocity.cross(
                self.orientation.xform(self.wheel_positions[i])
            )
            
            # Calculate tire force
            force = self.tire_physics.calculate_tire_force(
                wheel_velocity,
                self.wheel_speeds[i],
                self.wheel_angles[i],
                self.wheel_loads[i]
            )
            
            tire_forces.append(force)
        
        return tire_forces
    
    def apply_forces_and_torques(self, tire_forces, dt):
        """Apply all forces and torques to update vehicle state."""
        # Reset total force and torque
        self.total_force = Vec3(0, 0, 0)
        self.total_torque = Vec3(0, 0, 0)
        
        # Add tire forces
        for i, force in enumerate(tire_forces):
            # Transform force to world coordinates
            world_force = self.orientation.xform(force)
            self.total_force += world_force
            
            # Calculate torque about CG
            lever_arm = self.orientation.xform(self.wheel_positions[i])
            torque = lever_arm.cross(world_force)
            self.total_torque += torque
        
        # Add aerodynamic forces
        aero_force, aero_torque = self.aerodynamics.calculate_forces(self.velocity)
        self.total_force += aero_force
        self.total_torque += aero_torque
        
        # Add engine forces
        engine_force = self.engine.calculate_drive_force(self.throttle, self.wheel_speeds)
        # Apply engine force to driven wheels (rear wheels for RWD)
        drive_force_per_wheel = engine_force / 2  # Split between rear wheels
        rear_drive_force = Vec3(0, drive_force_per_wheel, 0)
        
        for i in [2, 3]:  # Rear wheels
            world_force = self.orientation.xform(rear_drive_force)
            self.total_force += world_force
            
            lever_arm = self.orientation.xform(self.wheel_positions[i])
            torque = lever_arm.cross(world_force)
            self.total_torque += torque
        
        # Add gravity
        self.total_force += Vec3(0, 0, -self.mass * 9.81)
        
        # Integrate motion
        self.integrate_motion(dt)
    
    def integrate_motion(self, dt):
        """Integrate equations of motion."""
        # Clamp dt to prevent instability
        dt = MathUtils.clamp(dt, 0.0, 1.0/30.0)  # Max 30 FPS timestep

        # Linear motion
        acceleration = self.total_force / self.mass

        # Clamp acceleration to reasonable values
        max_accel = 50.0  # m/s²
        if acceleration.length() > max_accel:
            acceleration = acceleration.normalized() * max_accel

        self.velocity += acceleration * dt

        # Clamp velocity to reasonable values
        max_velocity = 150.0  # m/s (540 km/h)
        if self.velocity.length() > max_velocity:
            self.velocity = self.velocity.normalized() * max_velocity

        self.position += self.velocity * dt
        
        # Angular motion (simplified)
        angular_acceleration = Vec3(
            self.total_torque.x / self.inertia.x,
            self.total_torque.y / self.inertia.y,
            self.total_torque.z / self.inertia.z
        )

        # Clamp angular acceleration
        max_angular_accel = 20.0  # rad/s²
        if angular_acceleration.length() > max_angular_accel:
            angular_acceleration = angular_acceleration.normalized() * max_angular_accel

        self.angular_velocity += angular_acceleration * dt

        # Clamp angular velocity
        max_angular_velocity = 10.0  # rad/s
        if self.angular_velocity.length() > max_angular_velocity:
            self.angular_velocity = self.angular_velocity.normalized() * max_angular_velocity
        
        # Update orientation
        angular_displacement = self.angular_velocity * dt
        angular_magnitude = angular_displacement.length()

        # Only apply rotation if there's significant angular displacement
        if angular_magnitude > 1e-6:  # Small threshold to avoid division by zero
            rotation_quat = Quat()
            rotation_quat.setFromAxisAngle(angular_magnitude, angular_displacement.normalized())
            self.orientation = self.orientation * rotation_quat
            self.orientation.normalize()
    
    def update(self, dt):
        """Update car physics simulation."""
        # Update subsystems
        self.engine.update(dt)
        self.suspension.update(dt)
        
        # Calculate wheel loads and steering
        self.calculate_wheel_loads()
        self.calculate_steering_angles()
        
        # Calculate tire forces
        tire_forces = self.calculate_tire_forces()
        
        # Apply forces and integrate motion
        self.apply_forces_and_torques(tire_forces, dt)
        
        # Update wheel speeds (simplified)
        self.update_wheel_speeds(dt)
    
    def update_wheel_speeds(self, dt):
        """Update individual wheel speeds."""
        # This is a simplified implementation
        # In reality, this would involve complex drivetrain modeling
        
        vehicle_speed = self.velocity.length()
        
        for i in range(4):
            # For now, assume wheels follow vehicle speed
            # This would be much more complex with proper drivetrain simulation
            self.wheel_speeds[i] = vehicle_speed
    
    def get_speed_kmh(self):
        """Get vehicle speed in km/h."""
        return self.velocity.length() * 3.6
    
    def get_local_velocity(self):
        """Get velocity in vehicle local coordinates."""
        return self.orientation.conjugate().xform(self.velocity)

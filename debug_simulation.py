#!/usr/bin/env python3
"""
Debug Racing Simulation
=======================

Step-by-step debugging of the racing simulation.
"""

import sys
import traceback

def test_step(step_name, test_func):
    """Test a specific step and report results."""
    print(f"Testing {step_name}...")
    try:
        result = test_func()
        print(f"✓ {step_name} - Success")
        return True
    except Exception as e:
        print(f"✗ {step_name} - Failed: {e}")
        traceback.print_exc()
        return False

def test_basic_imports():
    """Test basic Python imports."""
    import sys
    import os
    import math
    import time
    return True

def test_panda3d_imports():
    """Test Panda3D imports."""
    from direct.showbase.ShowBase import ShowBase
    from panda3d.core import Vec3, Quat
    from panda3d.bullet import BulletWorld
    return True

def test_numpy_imports():
    """Test NumPy and scientific libraries."""
    import numpy as np
    import scipy
    import matplotlib
    return True

def test_utils_imports():
    """Test utility module imports."""
    from utils.math_utils import MathUtils
    from utils.performance import PerformanceMonitor
    return True

def test_physics_imports():
    """Test physics module imports."""
    from physics.car_physics import CarPhysics
    from physics.tire_physics import TirePhysics
    from physics.suspension import SuspensionSystem
    from physics.aerodynamics import AerodynamicsSystem
    from physics.engine import EngineSystem
    return True

def test_world_imports():
    """Test world module imports."""
    from world.terrain_generator import TerrainGenerator
    from world.environment import Environment
    return True

def test_vehicle_imports():
    """Test vehicle module imports."""
    from vehicle.car import Car
    from vehicle.telemetry import TelemetrySystem
    return True

def test_input_imports():
    """Test input module imports."""
    from input.input_manager import InputManager
    return True

def test_physics_initialization():
    """Test physics system initialization."""
    from physics.car_physics import CarPhysics
    physics = CarPhysics()
    assert physics.mass > 0
    assert len(physics.wheel_positions) == 4
    return True

def test_minimal_panda3d():
    """Test minimal Panda3D setup."""
    from direct.showbase.ShowBase import ShowBase
    from panda3d.core import Vec3
    
    # This should not hang in headless mode
    import os
    os.environ['PANDA3D_WINDOW_TYPE'] = 'none'  # Headless mode
    
    class MinimalApp(ShowBase):
        def __init__(self):
            ShowBase.__init__(self)
            self.test_complete = True
    
    app = MinimalApp()
    assert hasattr(app, 'test_complete')
    app.destroy()
    return True

def main():
    """Run all debug tests."""
    print("Racing Simulation Debug Test")
    print("=" * 40)
    
    tests = [
        ("Basic Python Imports", test_basic_imports),
        ("Panda3D Imports", test_panda3d_imports),
        ("NumPy/SciPy Imports", test_numpy_imports),
        ("Utils Module Imports", test_utils_imports),
        ("Physics Module Imports", test_physics_imports),
        ("World Module Imports", test_world_imports),
        ("Vehicle Module Imports", test_vehicle_imports),
        ("Input Module Imports", test_input_imports),
        ("Physics Initialization", test_physics_initialization),
        ("Minimal Panda3D", test_minimal_panda3d),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_step(test_name, test_func):
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Debug Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The simulation should work.")
        print("\nTrying to run the main simulation...")
        
        try:
            # Try to import and create the main simulation
            from game_engine import RacingSimulation
            print("✓ Game engine imported successfully")
            
            # Set headless mode for testing
            import os
            os.environ['PANDA3D_WINDOW_TYPE'] = 'none'
            
            print("Creating simulation instance...")
            sim = RacingSimulation()
            print("✓ Simulation created successfully!")
            
            print("The simulation is working! To run with graphics:")
            print("  python main.py")
            
        except Exception as e:
            print(f"✗ Main simulation failed: {e}")
            traceback.print_exc()
    else:
        print("❌ Some tests failed. Check the errors above.")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())

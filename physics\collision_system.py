"""
Advanced Collision Detection and Response System
===============================================

Comprehensive collision handling for vehicles, terrain, and objects.
"""

import math
import numpy as np
from panda3d.core import *
from panda3d.bullet import *
from utils.math_utils import MathUtils

class CollisionSystem:
    """Advanced collision detection and response system."""
    
    def __init__(self, game_engine):
        """Initialize collision system."""
        self.game_engine = game_engine
        
        # Collision categories (bit masks)
        self.COLLISION_CATEGORIES = {
            'TERRAIN': 1,
            'VEHICLE': 2,
            'BARRIERS': 4,
            'OBJECTS': 8,
            'SENSORS': 16
        }
        
        # Collision response parameters
        self.vehicle_restitution = 0.3  # Bounciness
        self.vehicle_friction = 0.8     # Surface friction
        self.damage_threshold = 15.0    # m/s impact speed for damage
        
        # Collision callbacks
        self.collision_callbacks = {}
        
        # Damage system
        self.vehicle_damage = {
            'front': 0.0,    # 0-1 damage level
            'rear': 0.0,
            'left': 0.0,
            'right': 0.0,
            'suspension': [0.0, 0.0, 0.0, 0.0],  # Per wheel
            'engine': 0.0,
            'transmission': 0.0
        }
        
        # Collision history for analysis
        self.collision_history = []
        
        print("Collision system initialized")
    
    def setup_collision_detection(self):
        """Setup collision detection for all objects."""
        # Enable collision detection in physics world
        # Note: Contact callbacks may not be available in all Panda3D versions
        try:
            self.game_engine.physics_world.setContactAddedCallback(self.on_contact_added)
        except AttributeError:
            print("Contact callbacks not available - using alternative collision detection")
        
        # Setup collision shapes for different objects
        self.setup_vehicle_collision()
        self.setup_terrain_collision()
        self.setup_track_barriers()
        self.setup_environmental_objects()
    
    def setup_vehicle_collision(self):
        """Setup collision detection for the vehicle."""
        if not hasattr(self.game_engine, 'car'):
            return
        
        car = self.game_engine.car
        
        # Main vehicle body collision
        vehicle_shape = BulletBoxShape(Vec3(0.9, 2.2, 0.6))  # Car dimensions
        car.physics_body.addShape(vehicle_shape)
        
        # Set collision properties
        car.physics_body.setRestitution(self.vehicle_restitution)
        car.physics_body.setFriction(self.vehicle_friction)
        # Note: Rolling friction may not be available in all Panda3D versions
        
        # Set collision mask
        car.physics_body_np.setCollideMask(
            self.COLLISION_CATEGORIES['VEHICLE']
        )
        
        # Add individual wheel collision shapes
        self.setup_wheel_collisions(car)
    
    def setup_wheel_collisions(self, car):
        """Setup collision detection for individual wheels."""
        wheel_radius = car.physics.tire_physics.tire_radius
        wheel_width = car.physics.tire_physics.tire_width
        
        for i, wheel_pos in enumerate(car.physics.wheel_positions):
            # Create wheel collision shape
            wheel_shape = BulletCylinderShape(wheel_radius, wheel_width, ZUp)
            
            # Create separate rigid body for wheel
            wheel_body = BulletRigidBodyNode(f'wheel_{i}')
            wheel_body.setMass(car.physics.tire_physics.tire_mass)
            wheel_body.addShape(wheel_shape)
            wheel_body.setRestitution(0.1)
            wheel_body.setFriction(1.2)  # High friction for tires
            
            # Attach to scene
            wheel_np = car.visual_node.attachNewNode(wheel_body)
            wheel_np.setPos(wheel_pos)
            
            # Add to physics world
            self.game_engine.physics_world.attachRigidBody(wheel_body)
            
            # Store reference
            if not hasattr(car, 'wheel_bodies'):
                car.wheel_bodies = []
            car.wheel_bodies.append(wheel_body)
    
    def setup_terrain_collision(self):
        """Setup collision detection for terrain."""
        # Terrain collision is already handled in terrain_generator.py
        # Just set the collision mask
        if hasattr(self.game_engine, 'terrain'):
            # Find terrain physics body
            for child in self.game_engine.render.getChildren():
                if child.node().getName() == 'terrain':
                    child.setCollideMask(self.COLLISION_CATEGORIES['TERRAIN'])
    
    def setup_track_barriers(self):
        """Create collision barriers around the track."""
        # Create invisible barriers to keep cars on track
        barrier_height = 2.0
        barrier_thickness = 0.5
        
        # Get track boundaries (simplified - would use actual track data)
        track_bounds = [
            # Example barriers - would be generated from track data
            {'pos': Vec3(-100, 0, 0), 'size': Vec3(barrier_thickness, 200, barrier_height)},
            {'pos': Vec3(100, 0, 0), 'size': Vec3(barrier_thickness, 200, barrier_height)},
            {'pos': Vec3(0, -100, 0), 'size': Vec3(200, barrier_thickness, barrier_height)},
            {'pos': Vec3(0, 100, 0), 'size': Vec3(200, barrier_thickness, barrier_height)},
        ]
        
        for barrier_data in track_bounds:
            self.create_barrier(barrier_data['pos'], barrier_data['size'])
    
    def create_barrier(self, position, size):
        """Create a collision barrier."""
        # Create barrier shape
        barrier_shape = BulletBoxShape(size)
        
        # Create rigid body
        barrier_body = BulletRigidBodyNode('barrier')
        barrier_body.setMass(0)  # Static object
        barrier_body.addShape(barrier_shape)
        barrier_body.setRestitution(0.5)
        barrier_body.setFriction(0.8)
        
        # Add to scene
        barrier_np = self.game_engine.render.attachNewNode(barrier_body)
        barrier_np.setPos(position)
        barrier_np.setCollideMask(self.COLLISION_CATEGORIES['BARRIERS'])
        
        # Add to physics world
        self.game_engine.physics_world.attachRigidBody(barrier_body)
        
        # Optional: Add visual representation
        self.create_barrier_visual(barrier_np, size)
    
    def create_barrier_visual(self, barrier_np, size):
        """Create visual representation of barrier."""
        # Create simple box geometry
        from panda3d.core import CardMaker
        cm = CardMaker("barrier")
        cm.setFrame(-size.x, size.x, -size.z, size.z)
        
        barrier_visual = barrier_np.attachNewNode(cm.generate())
        barrier_visual.setColor(1, 0, 0, 0.3)  # Semi-transparent red
        barrier_visual.setTransparency(TransparencyAttrib.MAlpha)
        barrier_visual.setTwoSided(True)
    
    def setup_environmental_objects(self):
        """Setup collision for environmental objects."""
        # Create some environmental objects for collision testing
        objects = [
            {'type': 'cone', 'pos': Vec3(10, 20, 0), 'size': 1.0},
            {'type': 'barrel', 'pos': Vec3(-15, 30, 0), 'size': 0.8},
            {'type': 'tire_stack', 'pos': Vec3(25, -10, 0), 'size': 1.5},
        ]
        
        for obj_data in objects:
            self.create_environmental_object(obj_data)
    
    def create_environmental_object(self, obj_data):
        """Create an environmental collision object."""
        pos = obj_data['pos']
        size = obj_data['size']
        obj_type = obj_data['type']
        
        # Create appropriate collision shape
        if obj_type == 'cone':
            shape = BulletConeShape(size, size * 2, ZUp)
        elif obj_type == 'barrel':
            shape = BulletCylinderShape(size, size * 1.5, ZUp)
        else:  # tire_stack
            shape = BulletBoxShape(Vec3(size, size, size * 0.5))
        
        # Create rigid body
        obj_body = BulletRigidBodyNode(f'env_object_{obj_type}')
        obj_body.setMass(50.0)  # Moveable objects
        obj_body.addShape(shape)
        obj_body.setRestitution(0.4)
        obj_body.setFriction(0.6)
        
        # Add to scene
        obj_np = self.game_engine.render.attachNewNode(obj_body)
        obj_np.setPos(pos)
        obj_np.setCollideMask(self.COLLISION_CATEGORIES['OBJECTS'])
        
        # Add to physics world
        self.game_engine.physics_world.attachRigidBody(obj_body)
        
        # Add visual representation
        self.create_object_visual(obj_np, obj_type, size)
    
    def create_object_visual(self, obj_np, obj_type, size):
        """Create visual representation of environmental object."""
        # Simple colored shapes for now
        from panda3d.core import CardMaker
        cm = CardMaker(obj_type)
        cm.setFrame(-size, size, -size, size)
        
        visual = obj_np.attachNewNode(cm.generate())
        
        # Color based on object type
        if obj_type == 'cone':
            visual.setColor(1, 0.5, 0, 1)  # Orange
        elif obj_type == 'barrel':
            visual.setColor(0.5, 0.3, 0.1, 1)  # Brown
        else:  # tire_stack
            visual.setColor(0.2, 0.2, 0.2, 1)  # Dark gray
    
    def on_contact_added(self, contact):
        """Callback when collision contact is detected."""
        # Get the two objects in collision
        node0 = contact.getNode0()
        node1 = contact.getNode1()
        
        # Determine collision type
        collision_data = {
            'timestamp': globalClock.getFrameTime(),
            'node0': node0.getName(),
            'node1': node1.getName(),
            'contact_point': contact.getPositionWorldOnA(),
            'normal': contact.getNormalWorldOnB(),
            'impulse': contact.getAppliedImpulse()
        }
        
        # Process collision based on objects involved
        self.process_collision(collision_data)
        
        # Store in history
        self.collision_history.append(collision_data)
        
        # Keep only recent collisions
        if len(self.collision_history) > 1000:
            self.collision_history = self.collision_history[-500:]
    
    def process_collision(self, collision_data):
        """Process collision and apply effects."""
        node0_name = collision_data['node0']
        node1_name = collision_data['node1']
        impulse = collision_data['impulse']
        
        # Vehicle collision with terrain
        if 'car' in node0_name or 'car' in node1_name:
            if 'terrain' in node0_name or 'terrain' in node1_name:
                self.handle_vehicle_terrain_collision(collision_data)
            
            # Vehicle collision with barriers
            elif 'barrier' in node0_name or 'barrier' in node1_name:
                self.handle_vehicle_barrier_collision(collision_data)
            
            # Vehicle collision with objects
            elif 'env_object' in node0_name or 'env_object' in node1_name:
                self.handle_vehicle_object_collision(collision_data)
    
    def handle_vehicle_terrain_collision(self, collision_data):
        """Handle vehicle collision with terrain."""
        impulse_magnitude = collision_data['impulse']
        
        # Calculate impact speed
        impact_speed = impulse_magnitude / self.game_engine.car.physics.mass
        
        # Apply damage if impact is severe
        if impact_speed > self.damage_threshold:
            damage_amount = (impact_speed - self.damage_threshold) / 20.0
            self.apply_vehicle_damage('front', damage_amount * 0.1)
            
            # Create visual/audio feedback
            self.create_collision_effects(collision_data['contact_point'], impact_speed)
    
    def handle_vehicle_barrier_collision(self, collision_data):
        """Handle vehicle collision with barriers."""
        impulse_magnitude = collision_data['impulse']
        impact_speed = impulse_magnitude / self.game_engine.car.physics.mass
        
        # Barriers cause more damage
        if impact_speed > 5.0:  # Lower threshold for barriers
            damage_amount = impact_speed / 15.0
            self.apply_vehicle_damage('front', damage_amount * 0.2)
            
            # Strong collision effects
            self.create_collision_effects(collision_data['contact_point'], impact_speed)
            
            print(f"Barrier collision! Impact speed: {impact_speed:.1f} m/s")
    
    def handle_vehicle_object_collision(self, collision_data):
        """Handle vehicle collision with environmental objects."""
        impulse_magnitude = collision_data['impulse']
        impact_speed = impulse_magnitude / self.game_engine.car.physics.mass
        
        # Objects cause minor damage but affect handling
        if impact_speed > 3.0:
            damage_amount = impact_speed / 30.0
            self.apply_vehicle_damage('front', damage_amount * 0.05)
            
            print(f"Object collision! Impact speed: {impact_speed:.1f} m/s")
    
    def apply_vehicle_damage(self, location, amount):
        """Apply damage to vehicle."""
        self.vehicle_damage[location] = min(1.0, self.vehicle_damage[location] + amount)
        
        # Apply damage effects to vehicle performance
        if hasattr(self.game_engine, 'car'):
            self.apply_damage_effects()
    
    def apply_damage_effects(self):
        """Apply damage effects to vehicle performance."""
        car = self.game_engine.car
        
        # Engine damage reduces power
        engine_damage = self.vehicle_damage['engine']
        if engine_damage > 0:
            car.physics.engine.max_power *= (1.0 - engine_damage * 0.5)
        
        # Suspension damage affects handling
        for i, suspension_damage in enumerate(self.vehicle_damage['suspension']):
            if suspension_damage > 0:
                # Reduce suspension effectiveness
                car.physics.suspension.spring_rate *= (1.0 - suspension_damage * 0.3)
    
    def create_collision_effects(self, position, intensity):
        """Create visual and audio effects for collisions."""
        # Create particle effects (simplified)
        # In a full implementation, this would create sparks, debris, etc.
        
        # Create temporary visual effect
        from panda3d.core import CardMaker
        cm = CardMaker("collision_effect")
        cm.setFrame(-0.5, 0.5, -0.5, 0.5)
        
        effect = self.game_engine.render.attachNewNode(cm.generate())
        effect.setPos(position)
        effect.setColor(1, 1, 0, 1)  # Yellow flash
        effect.setScale(intensity * 0.1)
        
        # Remove effect after short time
        from direct.task import Task
        def remove_effect(task):
            effect.removeNode()
            return task.done
        
        taskMgr.doMethodLater(0.2, remove_effect, "remove_collision_effect")
    
    def get_collision_telemetry(self):
        """Get collision telemetry data."""
        recent_collisions = [c for c in self.collision_history 
                           if globalClock.getFrameTime() - c['timestamp'] < 10.0]
        
        return {
            'total_collisions': len(self.collision_history),
            'recent_collisions': len(recent_collisions),
            'vehicle_damage': self.vehicle_damage.copy(),
            'last_collision': self.collision_history[-1] if self.collision_history else None
        }
    
    def reset_vehicle_damage(self):
        """Reset vehicle damage (for repairs/resets)."""
        for key in self.vehicle_damage:
            if isinstance(self.vehicle_damage[key], list):
                self.vehicle_damage[key] = [0.0] * len(self.vehicle_damage[key])
            else:
                self.vehicle_damage[key] = 0.0
        
        print("Vehicle damage reset")
    
    def update(self, dt):
        """Update collision system."""
        # Process any pending collision callbacks
        # Update damage effects over time
        # Clean up old collision history
        pass

#!/usr/bin/env python3
"""
Advanced Car Racing Simulation
==============================

A comprehensive racing simulation using Panda3D with realistic physics,
procedural world generation, and advanced car dynamics.

Author: Augment Agent
License: MIT
"""

import sys
import os
from direct.showbase.ShowBase import ShowBase
from direct.task import Task
from panda3d.core import *
import numpy as np

# Add project modules to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_engine import RacingSimulation

def main():
    """Main entry point for the racing simulation."""
    print("=" * 60)
    print("Advanced Car Racing Simulation")
    print("Initializing Panda3D Engine...")
    print("=" * 60)
    
    try:
        # Create and run the simulation
        simulation = RacingSimulation()
        simulation.run()
    except Exception as e:
        print(f"Error starting simulation: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

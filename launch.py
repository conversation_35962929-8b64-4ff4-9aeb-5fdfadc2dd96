#!/usr/bin/env python3
"""
Quick Launch Script for Advanced Car Racing Simulation
======================================================

This script provides a simple way to launch the simulation with
different configurations and options.
"""

import sys
import os
import argparse
from pathlib import Path

def check_dependencies():
    """Quick check for essential dependencies."""
    try:
        import panda3d
        import numpy
        return True
    except ImportError as e:
        print(f"Missing dependency: {e}")
        print("Please run 'python setup.py' first to install dependencies.")
        return False

def launch_simulation(args):
    """Launch the main simulation."""
    if not check_dependencies():
        return 1
    
    print("Starting Advanced Car Racing Simulation...")
    print("=" * 50)
    
    # Set environment variables based on arguments
    if args.debug:
        os.environ['RACING_SIM_DEBUG'] = '1'
    
    if args.performance:
        os.environ['RACING_SIM_PERFORMANCE'] = '1'
    
    if args.telemetry:
        os.environ['RACING_SIM_TELEMETRY'] = '1'
    
    # Import and run the main simulation
    try:
        from main import main
        return main()
    except Exception as e:
        print(f"Error starting simulation: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_controls():
    """Display control information."""
    print("Advanced Car Racing Simulation - Controls")
    print("=" * 45)
    print("\nKeyboard Controls:")
    print("  W / ↑ Arrow    - Throttle")
    print("  S / ↓ Arrow    - Brake")
    print("  A / ← Arrow    - Steer Left")
    print("  D / → Arrow    - Steer Right")
    print("  Space          - Clutch")
    print("  Q              - Gear Down")
    print("  E              - Gear Up")
    print("  X              - Handbrake")
    print("  C              - Change Camera Mode")
    print("  R              - Reset Car")
    print("  ESC            - Exit")
    
    print("\nGamepad Controls:")
    print("  Right Trigger  - Throttle")
    print("  Left Trigger   - Brake")
    print("  Left Stick     - Steering")
    print("  Left Bumper    - Clutch")
    print("  Right Bumper   - Handbrake")
    print("  X Button       - Gear Down")
    print("  B Button       - Gear Up")
    print("  Y Button       - Change Camera")
    
    print("\nCamera Modes:")
    print("  Chase Camera   - Follow car from behind")
    print("  Cockpit Camera - Driver's view inside car")
    print("  Hood Camera    - View from car hood")
    print("  Wheel Camera   - Close-up wheel view")
    
    print("\nTelemetry:")
    print("  Real-time data is displayed on screen")
    print("  Data is automatically logged during sessions")
    print("  Use --telemetry flag for detailed logging")

def show_features():
    """Display simulation features."""
    print("Advanced Car Racing Simulation - Features")
    print("=" * 45)
    print("\nPhysics Simulation:")
    print("  ✓ Realistic tire physics with Pacejka model")
    print("  ✓ Advanced suspension dynamics")
    print("  ✓ Engine and transmission modeling")
    print("  ✓ Aerodynamic forces and downforce")
    print("  ✓ Weight transfer and vehicle dynamics")
    
    print("\nWorld Generation:")
    print("  ✓ Procedural terrain generation")
    print("  ✓ Multiple racing tracks")
    print("  ✓ Dynamic weather system")
    print("  ✓ Day/night cycle")
    print("  ✓ Environmental effects")
    
    print("\nTelemetry & Analysis:")
    print("  ✓ Real-time data logging")
    print("  ✓ Performance monitoring")
    print("  ✓ Tire temperature and wear")
    print("  ✓ Suspension telemetry")
    print("  ✓ Engine diagnostics")
    
    print("\nInput Support:")
    print("  ✓ Keyboard controls")
    print("  ✓ Gamepad/Controller support")
    print("  ✓ Steering wheel compatibility")
    print("  ✓ Force feedback (when supported)")

def main():
    """Main launcher function."""
    parser = argparse.ArgumentParser(
        description="Advanced Car Racing Simulation Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launch.py                    # Start simulation normally
  python launch.py --debug            # Start with debug output
  python launch.py --performance      # Enable performance monitoring
  python launch.py --telemetry        # Enable detailed telemetry
  python launch.py --controls         # Show control information
  python launch.py --features         # Show simulation features
        """
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug output and additional logging'
    )
    
    parser.add_argument(
        '--performance',
        action='store_true',
        help='Enable performance monitoring and profiling'
    )
    
    parser.add_argument(
        '--telemetry',
        action='store_true',
        help='Enable detailed telemetry logging'
    )
    
    parser.add_argument(
        '--controls',
        action='store_true',
        help='Show control information and exit'
    )
    
    parser.add_argument(
        '--features',
        action='store_true',
        help='Show simulation features and exit'
    )
    
    parser.add_argument(
        '--setup',
        action='store_true',
        help='Run setup and dependency installation'
    )
    
    args = parser.parse_args()
    
    # Handle information commands
    if args.controls:
        show_controls()
        return 0
    
    if args.features:
        show_features()
        return 0
    
    if args.setup:
        print("Running setup...")
        try:
            import setup
            return setup.main()
        except ImportError:
            print("Setup script not found. Please ensure setup.py is in the current directory.")
            return 1
    
    # Launch simulation
    return launch_simulation(args)

if __name__ == "__main__":
    sys.exit(main())

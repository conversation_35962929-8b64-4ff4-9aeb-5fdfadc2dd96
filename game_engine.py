"""
Core Game Engine for Racing Simulation
======================================

Main game engine class that orchestrates all simulation components.
"""

from direct.showbase.ShowBase import ShowBase
from direct.task import Task
from direct.gui.OnscreenText import OnscreenText
from direct.gui.DirectGui import *
from panda3d.core import *
from panda3d.bullet import *
import numpy as np
import time

from physics.car_physics import CarPhysics
from physics.collision_system import CollisionSystem
from world.terrain_generator import TerrainGenerator
from world.environment import Environment
from vehicle.car import Car
from vehicle.telemetry import TelemetrySystem
from input.input_manager import InputManager
from utils.performance import PerformanceMonitor

class RacingSimulation(ShowBase):
    """Main simulation class that manages all game systems."""
    
    def __init__(self):
        ShowBase.__init__(self)
        
        # Initialize core systems
        self.setup_graphics()
        self.setup_physics()
        self.setup_lighting()
        self.setup_camera()
        
        # Initialize game systems
        self.terrain_generator = TerrainGenerator(self)
        self.environment = Environment(self)
        self.collision_system = CollisionSystem(self)
        self.input_manager = InputManager(self)
        self.telemetry = TelemetrySystem(self)
        self.performance_monitor = PerformanceMonitor(self)
        
        # Create the world
        self.create_world()
        
        # Create the car
        self.car = Car(self, position=Vec3(0, 0, 5))

        # Setup collision detection
        self.collision_system.setup_collision_detection()

        # Setup UI
        self.setup_ui()
        
        # Start main tasks
        self.start_tasks()
        
        print("Racing simulation initialized successfully!")
    
    def setup_graphics(self):
        """Configure graphics settings for optimal performance."""
        # Enable advanced rendering features
        self.render.setShaderAuto()
        
        # Configure window
        props = WindowProperties()
        props.setTitle("Advanced Racing Simulation")
        props.setSize(1920, 1080)
        self.win.requestProperties(props)
        
        # Enable multisampling for anti-aliasing
        self.render.setAntialias(AntialiasAttrib.MAuto)
        
        # Configure frame rate
        globalClock.setMode(ClockObject.MLimited)
        globalClock.setFrameRate(120)  # Target 120 FPS, will adapt based on performance
    
    def setup_physics(self):
        """Initialize the Bullet physics world."""
        # Create physics world
        self.physics_world = BulletWorld()
        self.physics_world.setGravity(Vec3(0, 0, -9.81))
        
        # Configure physics parameters for racing simulation
        self.physics_world.getWorldInfo().setAirDensity(1.2)  # kg/m³
        # Note: Linear and angular damping are set per rigid body, not globally
        
        # Start physics task
        taskMgr.add(self.update_physics, "update_physics")
    
    def setup_lighting(self):
        """Setup realistic lighting system."""
        # Disable default lighting
        self.render.clearLight()
        
        # Create sun (directional light)
        self.sun = DirectionalLight('sun')
        self.sun.setColor(VBase4(1.0, 0.95, 0.8, 1))
        self.sun.setDirection(Vec3(-1, -1, -1))
        self.sun_np = self.render.attachNewNode(self.sun)
        self.render.setLight(self.sun_np)
        
        # Create ambient light
        self.ambient = AmbientLight('ambient')
        self.ambient.setColor(VBase4(0.3, 0.3, 0.4, 1))
        self.ambient_np = self.render.attachNewNode(self.ambient)
        self.render.setLight(self.ambient_np)
        
        # Enable shadows
        self.sun.setShadowCaster(True, 2048, 2048)
        self.render.setShaderAuto()
    
    def setup_camera(self):
        """Configure camera system."""
        # Disable default camera controls
        self.disableMouse()
        
        # Set initial camera position (will be updated by car)
        self.camera.setPos(0, -20, 10)
        self.camera.lookAt(0, 0, 0)
        
        # Configure camera properties
        lens = self.cam.node().getLens()
        lens.setFov(75)  # Field of view
        lens.setNear(0.1)
        lens.setFar(10000)
    
    def create_world(self):
        """Generate the racing world."""
        print("Generating world terrain...")
        self.terrain = self.terrain_generator.generate_terrain(size=2000, detail=512)
        
        print("Creating racing tracks...")
        self.tracks = self.terrain_generator.generate_tracks(num_tracks=3)
        
        print("Setting up environment...")
        self.environment.setup_sky()
        self.environment.setup_weather()
    
    def setup_ui(self):
        """Create the user interface."""
        # Performance display
        self.fps_text = OnscreenText(
            text="FPS: 0",
            pos=(-1.3, 0.9),
            scale=0.05,
            fg=(1, 1, 1, 1)
        )
        
        # Speed display
        self.speed_text = OnscreenText(
            text="Speed: 0 km/h",
            pos=(-1.3, 0.8),
            scale=0.05,
            fg=(1, 1, 1, 1)
        )
        
        # Telemetry display
        self.telemetry_text = OnscreenText(
            text="Telemetry: Ready",
            pos=(-1.3, 0.7),
            scale=0.05,
            fg=(1, 1, 1, 1)
        )

        # Collision display
        self.collision_text = OnscreenText(
            text="Damage: None",
            pos=(-1.3, 0.6),
            scale=0.05,
            fg=(1, 1, 1, 1)
        )
    
    def start_tasks(self):
        """Start all recurring tasks."""
        taskMgr.add(self.update_simulation, "update_simulation")
        taskMgr.add(self.update_ui, "update_ui")
        taskMgr.add(self.update_camera, "update_camera")
    
    def update_physics(self, task):
        """Update physics simulation."""
        dt = globalClock.getDt()
        if dt > 0:
            # Limit physics timestep for stability
            physics_dt = min(dt, 1.0/60.0)
            self.physics_world.doPhysics(physics_dt, 10, 1.0/180.0)
        return task.cont
    
    def update_simulation(self, task):
        """Main simulation update loop."""
        dt = globalClock.getDt()
        
        # Update input
        self.input_manager.update(dt)
        
        # Update car
        if hasattr(self, 'car'):
            self.car.update(dt)
        
        # Update environment
        self.environment.update(dt)

        # Update collision system
        self.collision_system.update(dt)

        # Update telemetry
        self.telemetry.update(dt)
        
        return task.cont
    
    def update_ui(self, task):
        """Update user interface elements."""
        # Update FPS
        fps = globalClock.getAverageFrameRate()
        self.fps_text.setText(f"FPS: {fps:.1f}")
        
        # Update speed
        if hasattr(self, 'car'):
            speed_kmh = self.car.get_speed_kmh()
            self.speed_text.setText(f"Speed: {speed_kmh:.1f} km/h")
        
        # Update telemetry info
        if hasattr(self, 'car'):
            gear = self.car.get_current_gear()
            rpm = self.car.get_engine_rpm()
            self.telemetry_text.setText(f"Gear: {gear} | RPM: {rpm:.0f}")

        # Update collision info
        if hasattr(self, 'collision_system'):
            collision_data = self.collision_system.get_collision_telemetry()

            # Calculate total damage properly
            total_damage = 0.0
            for key, value in collision_data['vehicle_damage'].items():
                if isinstance(value, list):
                    total_damage += sum(value)
                else:
                    total_damage += value

            if total_damage > 0:
                self.collision_text.setText(f"Damage: {total_damage:.1%}")
                # Change color based on damage level
                if total_damage > 0.5:
                    self.collision_text['fg'] = (1, 0, 0, 1)  # Red for high damage
                elif total_damage > 0.2:
                    self.collision_text['fg'] = (1, 1, 0, 1)  # Yellow for medium damage
                else:
                    self.collision_text['fg'] = (1, 0.5, 0, 1)  # Orange for low damage
            else:
                self.collision_text.setText("Damage: None")
                self.collision_text['fg'] = (1, 1, 1, 1)  # White for no damage
        
        return task.cont
    
    def update_camera(self, task):
        """Update camera to follow the car."""
        if hasattr(self, 'car'):
            self.car.update_camera(self.camera)
        return task.cont

"""
Advanced Engine and Drivetrain System
=====================================

Realistic engine modeling with torque curves, transmission, and drivetrain.
"""

import math
import numpy as np
from panda3d.core import Vec3
from utils.math_utils import MathUtils

class EngineSystem:
    """Advanced engine and drivetrain simulation."""
    
    def __init__(self, car_physics):
        """Initialize engine system."""
        self.car_physics = car_physics
        
        # Engine parameters
        self.displacement = 3.0  # Liters
        self.max_power = 350000  # Watts (350 kW / 469 HP)
        self.max_torque = 450    # Nm
        self.max_rpm = 7500
        self.idle_rpm = 800
        self.redline_rpm = 7000
        
        # Current engine state
        self.current_rpm = self.idle_rpm
        self.current_torque = 0.0
        self.current_power = 0.0
        self.engine_temp = 90.0  # Celsius
        
        # Transmission parameters
        self.gear_ratios = [3.36, 2.05, 1.48, 1.16, 0.97, 0.81]  # 6-speed manual
        self.reverse_ratio = -3.36
        self.final_drive_ratio = 3.73
        self.current_gear = 1
        self.clutch_engagement = 1.0  # 0 = disengaged, 1 = fully engaged
        
        # Drivetrain parameters
        self.drivetrain_efficiency = 0.85  # 85% efficiency
        self.drivetrain_inertia = 0.3  # kg⋅m²
        
        # Engine characteristics
        self.torque_curve = self.create_torque_curve()
        self.fuel_consumption_rate = 0.0  # L/s
        
        # Engine braking
        self.engine_braking_factor = 0.02
        
        # Turbo/Supercharger (optional)
        self.forced_induction = False
        self.boost_pressure = 0.0  # bar
        self.max_boost = 1.5  # bar
        
        print("Engine system initialized")
    
    def create_torque_curve(self):
        """Create realistic engine torque curve."""
        # RPM points for torque curve
        rpm_points = np.array([
            self.idle_rpm, 1500, 2000, 3000, 4000, 5000, 
            6000, self.redline_rpm, self.max_rpm
        ])
        
        # Torque values at each RPM point (Nm)
        torque_points = np.array([
            50,    # Idle
            200,   # 1500 RPM
            300,   # 2000 RPM
            420,   # 3000 RPM
            450,   # 4000 RPM (peak torque)
            440,   # 5000 RPM
            400,   # 6000 RPM
            350,   # 7000 RPM (redline)
            0      # Max RPM (fuel cut)
        ])
        
        return {'rpm': rpm_points, 'torque': torque_points}
    
    def get_engine_torque(self, rpm, throttle_position):
        """
        Calculate engine torque at given RPM and throttle position.
        
        Args:
            rpm: Engine RPM
            throttle_position: Throttle position (0-1)
        
        Returns:
            Engine torque in Nm
        """
        if rpm <= 0 or rpm > self.max_rpm:
            return 0.0
        
        # Interpolate torque from curve
        base_torque = np.interp(rpm, self.torque_curve['rpm'], self.torque_curve['torque'])
        
        # Apply throttle position
        torque = base_torque * throttle_position
        
        # Apply forced induction boost
        if self.forced_induction:
            boost_factor = 1.0 + (self.boost_pressure / self.max_boost) * 0.4
            torque *= boost_factor
        
        # Engine temperature effects
        temp_factor = self.get_temperature_factor()
        torque *= temp_factor
        
        return torque
    
    def get_temperature_factor(self):
        """Get torque multiplier based on engine temperature."""
        optimal_temp = 90.0  # Celsius
        
        if self.engine_temp < 60:
            # Cold engine - reduced power
            return 0.8 + (self.engine_temp - 20) / 40 * 0.2
        elif self.engine_temp > 110:
            # Overheating - reduced power
            return max(0.5, 1.0 - (self.engine_temp - 110) / 20 * 0.5)
        else:
            # Normal operating temperature
            return 1.0
    
    def calculate_gear_ratio(self):
        """Calculate total gear ratio for current gear."""
        if self.current_gear == 0:  # Neutral
            return 0.0
        elif self.current_gear == -1:  # Reverse
            return self.reverse_ratio * self.final_drive_ratio
        else:
            return self.gear_ratios[self.current_gear - 1] * self.final_drive_ratio
    
    def calculate_wheel_torque(self, engine_torque):
        """Calculate torque at the wheels."""
        gear_ratio = self.calculate_gear_ratio()
        
        if gear_ratio == 0:
            return 0.0
        
        # Apply gear ratio and drivetrain efficiency
        wheel_torque = (engine_torque * abs(gear_ratio) * 
                       self.drivetrain_efficiency * self.clutch_engagement)
        
        return wheel_torque
    
    def calculate_drive_force(self, throttle_position, wheel_speeds):
        """
        Calculate drive force at the wheels.
        
        Args:
            throttle_position: Throttle input (0-1)
            wheel_speeds: List of wheel speeds [FL, FR, RL, RR]
        
        Returns:
            Total drive force in Newtons
        """
        # Calculate average driven wheel speed (rear wheels for RWD)
        avg_driven_wheel_speed = (wheel_speeds[2] + wheel_speeds[3]) / 2
        
        # Calculate engine RPM from wheel speed
        gear_ratio = self.calculate_gear_ratio()
        if gear_ratio != 0 and avg_driven_wheel_speed > 0:
            wheel_rpm = (avg_driven_wheel_speed * 60) / (2 * math.pi * self.car_physics.tire_physics.tire_radius)
            target_engine_rpm = wheel_rpm * abs(gear_ratio)
        else:
            target_engine_rpm = self.idle_rpm
        
        # Update engine RPM (with some lag for realism)
        rpm_change = (target_engine_rpm - self.current_rpm) * 0.1
        self.current_rpm = max(self.idle_rpm, self.current_rpm + rpm_change)
        
        # Calculate engine torque
        engine_torque = self.get_engine_torque(self.current_rpm, throttle_position)
        self.current_torque = engine_torque
        
        # Calculate power
        self.current_power = (engine_torque * self.current_rpm * 2 * math.pi) / 60
        
        # Calculate wheel torque
        wheel_torque = self.calculate_wheel_torque(engine_torque)
        
        # Convert to drive force
        tire_radius = self.car_physics.tire_physics.tire_radius
        drive_force = wheel_torque / tire_radius
        
        # Add engine braking when throttle is closed
        if throttle_position < 0.1 and self.current_rpm > self.idle_rpm:
            engine_braking = self.calculate_engine_braking()
            drive_force -= engine_braking
        
        return drive_force
    
    def calculate_engine_braking(self):
        """Calculate engine braking force."""
        # Engine braking increases with RPM
        rpm_factor = (self.current_rpm - self.idle_rpm) / (self.redline_rpm - self.idle_rpm)
        rpm_factor = MathUtils.clamp(rpm_factor, 0.0, 1.0)
        
        # Base engine braking force
        base_braking = 2000  # Newtons
        
        # Apply gear ratio (lower gears = more engine braking)
        gear_ratio = abs(self.calculate_gear_ratio())
        gear_factor = min(2.0, gear_ratio / 2.0)
        
        engine_braking = base_braking * rpm_factor * gear_factor * self.engine_braking_factor
        
        return engine_braking
    
    def shift_gear(self, target_gear):
        """Shift to target gear."""
        if target_gear < -1 or target_gear > len(self.gear_ratios):
            return False
        
        if target_gear != self.current_gear:
            self.current_gear = target_gear
            print(f"Shifted to gear {target_gear}")
            return True
        
        return False
    
    def auto_shift_logic(self):
        """Automatic transmission shift logic."""
        if self.current_gear == 0:  # Don't auto-shift in neutral
            return
        
        # Upshift logic
        if (self.current_rpm > self.redline_rpm * 0.9 and 
            self.current_gear < len(self.gear_ratios)):
            self.shift_gear(self.current_gear + 1)
        
        # Downshift logic
        elif (self.current_rpm < self.idle_rpm * 1.5 and 
              self.current_gear > 1):
            self.shift_gear(self.current_gear - 1)
    
    def update_engine_temperature(self, dt):
        """Update engine temperature based on load and cooling."""
        # Heat generation based on power output
        power_factor = self.current_power / self.max_power
        heat_generation = power_factor * 2.0  # Degrees per second
        
        # Cooling (simplified)
        ambient_temp = 25.0  # Celsius
        cooling_rate = (self.engine_temp - ambient_temp) * 0.01
        
        # Update temperature
        self.engine_temp += (heat_generation - cooling_rate) * dt
        self.engine_temp = MathUtils.clamp(self.engine_temp, ambient_temp, 130.0)
    
    def update_fuel_consumption(self, dt):
        """Update fuel consumption based on engine load."""
        # Simplified fuel consumption model
        # Base consumption + load-dependent consumption
        base_consumption = 0.0002  # L/s at idle
        load_consumption = (self.current_power / self.max_power) * 0.003  # L/s at full load
        
        self.fuel_consumption_rate = base_consumption + load_consumption
    
    def get_engine_telemetry(self):
        """Get engine telemetry data."""
        return {
            'rpm': self.current_rpm,
            'torque': self.current_torque,
            'power_kw': self.current_power / 1000,
            'power_hp': self.current_power / 745.7,
            'gear': self.current_gear,
            'engine_temp': self.engine_temp,
            'fuel_consumption': self.fuel_consumption_rate * 3600,  # L/h
            'boost_pressure': self.boost_pressure if self.forced_induction else 0,
            'clutch_engagement': self.clutch_engagement
        }
    
    def update(self, dt):
        """Update engine system."""
        # Update engine temperature
        self.update_engine_temperature(dt)
        
        # Update fuel consumption
        self.update_fuel_consumption(dt)
        
        # Auto-shift logic (if enabled)
        # self.auto_shift_logic()  # Uncomment for automatic transmission
        
        # Update boost pressure for forced induction
        if self.forced_induction:
            self.update_boost_pressure(dt)
    
    def update_boost_pressure(self, dt):
        """Update turbo/supercharger boost pressure."""
        # Simplified boost model
        throttle = getattr(self.car_physics, 'throttle', 0.0)
        rpm_factor = (self.current_rpm - self.idle_rpm) / (self.redline_rpm - self.idle_rpm)
        rpm_factor = MathUtils.clamp(rpm_factor, 0.0, 1.0)
        
        target_boost = throttle * rpm_factor * self.max_boost
        
        # Boost builds up with some lag
        boost_change = (target_boost - self.boost_pressure) * 2.0 * dt
        self.boost_pressure = MathUtils.clamp(
            self.boost_pressure + boost_change, 0.0, self.max_boost
        )

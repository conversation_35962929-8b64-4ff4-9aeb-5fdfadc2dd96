"""
Performance Monitoring and Optimization
======================================

Tools for monitoring and optimizing simulation performance.
"""

import time
import numpy as np
from collections import deque
from panda3d.core import *

class PerformanceMonitor:
    """Monitor and optimize simulation performance."""
    
    def __init__(self, game_engine):
        self.game_engine = game_engine
        
        # Performance metrics
        self.frame_times = deque(maxlen=120)  # Store last 2 seconds at 60 FPS
        self.physics_times = deque(maxlen=120)
        self.render_times = deque(maxlen=120)
        
        # Timing variables
        self.last_frame_time = time.time()
        self.physics_start_time = 0
        self.render_start_time = 0
        
        # Performance targets
        self.target_fps = 60
        self.target_frame_time = 1.0 / self.target_fps
        
        # Adaptive quality settings
        self.current_quality = "high"
        self.quality_levels = {
            "low": {
                "shadow_resolution": 512,
                "terrain_detail": 256,
                "particle_count": 100,
                "physics_substeps": 5
            },
            "medium": {
                "shadow_resolution": 1024,
                "terrain_detail": 512,
                "particle_count": 250,
                "physics_substeps": 8
            },
            "high": {
                "shadow_resolution": 2048,
                "terrain_detail": 1024,
                "particle_count": 500,
                "physics_substeps": 10
            }
        }
        
        print("Performance monitor initialized")
    
    def start_frame_timing(self):
        """Start timing a frame."""
        current_time = time.time()
        frame_time = current_time - self.last_frame_time
        self.frame_times.append(frame_time)
        self.last_frame_time = current_time
    
    def start_physics_timing(self):
        """Start timing physics calculations."""
        self.physics_start_time = time.time()
    
    def end_physics_timing(self):
        """End timing physics calculations."""
        if self.physics_start_time > 0:
            physics_time = time.time() - self.physics_start_time
            self.physics_times.append(physics_time)
            self.physics_start_time = 0
    
    def start_render_timing(self):
        """Start timing render operations."""
        self.render_start_time = time.time()
    
    def end_render_timing(self):
        """End timing render operations."""
        if self.render_start_time > 0:
            render_time = time.time() - self.render_start_time
            self.render_times.append(render_time)
            self.render_start_time = 0
    
    def get_average_fps(self):
        """Get average FPS over recent frames."""
        if len(self.frame_times) == 0:
            return 0
        
        avg_frame_time = np.mean(self.frame_times)
        if avg_frame_time > 0:
            return 1.0 / avg_frame_time
        return 0
    
    def get_frame_time_stats(self):
        """Get frame time statistics."""
        if len(self.frame_times) == 0:
            return {"avg": 0, "min": 0, "max": 0, "std": 0}
        
        times = np.array(self.frame_times)
        return {
            "avg": np.mean(times),
            "min": np.min(times),
            "max": np.max(times),
            "std": np.std(times)
        }
    
    def get_physics_time_stats(self):
        """Get physics timing statistics."""
        if len(self.physics_times) == 0:
            return {"avg": 0, "min": 0, "max": 0}
        
        times = np.array(self.physics_times)
        return {
            "avg": np.mean(times),
            "min": np.min(times),
            "max": np.max(times)
        }
    
    def should_adjust_quality(self):
        """Determine if quality settings should be adjusted."""
        if len(self.frame_times) < 60:  # Need enough samples
            return False
        
        avg_fps = self.get_average_fps()
        
        # If FPS is too low, reduce quality
        if avg_fps < self.target_fps * 0.8:
            return "reduce"
        
        # If FPS is consistently high, increase quality
        if avg_fps > self.target_fps * 1.2 and self.current_quality != "high":
            return "increase"
        
        return False
    
    def adjust_quality(self, direction):
        """Adjust quality settings based on performance."""
        quality_order = ["low", "medium", "high"]
        current_index = quality_order.index(self.current_quality)
        
        if direction == "reduce" and current_index > 0:
            self.current_quality = quality_order[current_index - 1]
            self.apply_quality_settings()
            print(f"Performance: Reduced quality to {self.current_quality}")
        
        elif direction == "increase" and current_index < len(quality_order) - 1:
            self.current_quality = quality_order[current_index + 1]
            self.apply_quality_settings()
            print(f"Performance: Increased quality to {self.current_quality}")
    
    def apply_quality_settings(self):
        """Apply current quality settings to the engine."""
        settings = self.quality_levels[self.current_quality]
        
        # Adjust shadow resolution
        if hasattr(self.game_engine, 'sun'):
            shadow_size = settings["shadow_resolution"]
            self.game_engine.sun.setShadowCaster(True, shadow_size, shadow_size)
        
        # Adjust terrain detail (would need terrain system integration)
        # This would be implemented when the terrain system is created
        
        # Adjust physics substeps
        if hasattr(self.game_engine, 'physics_world'):
            # This would adjust physics quality
            pass
    
    def get_memory_usage(self):
        """Get current memory usage statistics."""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return {
                "rss": memory_info.rss / 1024 / 1024,  # MB
                "vms": memory_info.vms / 1024 / 1024,  # MB
                "percent": process.memory_percent()
            }
        except ImportError:
            return {"rss": 0, "vms": 0, "percent": 0}
    
    def log_performance_stats(self):
        """Log current performance statistics."""
        frame_stats = self.get_frame_time_stats()
        physics_stats = self.get_physics_time_stats()
        memory_stats = self.get_memory_usage()
        
        print(f"Performance Stats:")
        print(f"  FPS: {self.get_average_fps():.1f}")
        print(f"  Frame Time: {frame_stats['avg']*1000:.2f}ms (±{frame_stats['std']*1000:.2f}ms)")
        print(f"  Physics Time: {physics_stats['avg']*1000:.2f}ms")
        print(f"  Memory: {memory_stats['rss']:.1f}MB ({memory_stats['percent']:.1f}%)")
        print(f"  Quality: {self.current_quality}")
    
    def update(self, dt):
        """Update performance monitoring."""
        self.start_frame_timing()
        
        # Check if quality adjustment is needed
        adjustment = self.should_adjust_quality()
        if adjustment:
            self.adjust_quality(adjustment)
    
    def enable_profiling(self):
        """Enable detailed profiling."""
        # Enable Panda3D's built-in profiling
        PStatClient.connect()
        
    def create_performance_graph(self):
        """Create a real-time performance graph."""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.animation as animation
            
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 6))
            
            def update_graph(frame):
                if len(self.frame_times) > 0:
                    # FPS graph
                    ax1.clear()
                    fps_data = [1.0/ft if ft > 0 else 0 for ft in self.frame_times]
                    ax1.plot(fps_data)
                    ax1.set_title("FPS")
                    ax1.set_ylim(0, 120)
                    ax1.axhline(y=60, color='r', linestyle='--', label='Target')
                    
                    # Frame time graph
                    ax2.clear()
                    frame_time_ms = [ft * 1000 for ft in self.frame_times]
                    ax2.plot(frame_time_ms)
                    ax2.set_title("Frame Time (ms)")
                    ax2.set_ylim(0, 50)
                    ax2.axhline(y=16.67, color='r', linestyle='--', label='60 FPS Target')
            
            ani = animation.FuncAnimation(fig, update_graph, interval=100)
            plt.tight_layout()
            plt.show()
            
        except ImportError:
            print("Matplotlib not available for performance graphing")

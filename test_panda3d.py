#!/usr/bin/env python3
"""
Simple Panda3D Test
==================

Test if Panda3D can create a window and basic 3D scene.
"""

from direct.showbase.ShowBase import ShowBase
from panda3d.core import *
from direct.task import Task
import sys

class SimpleTest(ShowBase):
    def __init__(self):
        ShowBase.__init__(self)
        
        print("Panda3D window created successfully!")
        
        # Create a simple cube
        self.cube = self.loader.loadModel("environment")
        if not self.cube:
            # Create a simple cube manually
            from panda3d.core import CardMaker
            cm = CardMaker("cube")
            cm.setFrame(-1, 1, -1, 1)
            self.cube = self.render.attachNewNode(cm.generate())
            self.cube.setColor(1, 0, 0, 1)  # Red cube
        
        self.cube.reparentTo(self.render)
        self.cube.setScale(2, 2, 2)
        self.cube.setPos(0, 10, 0)
        
        # Set camera position
        self.camera.setPos(0, -20, 5)
        self.camera.lookAt(0, 0, 0)
        
        # Add some text
        from direct.gui.OnscreenText import OnscreenText
        self.text = OnscreenText(
            text="Panda3D Test - Press ESC to exit",
            pos=(0, 0.9),
            scale=0.07,
            fg=(1, 1, 1, 1)
        )
        
        # Accept escape key to exit
        self.accept("escape", sys.exit)
        
        # Start rotation task
        taskMgr.add(self.rotate_cube, "rotate_cube")
        
        print("Use WASD to move, mouse to look around")
        print("Press ESC to exit")
    
    def rotate_cube(self, task):
        """Rotate the cube continuously."""
        angle = task.time * 40  # 40 degrees per second
        self.cube.setHpr(angle, angle, 0)
        return task.cont

def main():
    print("Starting Panda3D test...")
    try:
        app = SimpleTest()
        app.run()
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    return 0

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
"""
Installation Test Script
========================

Quick test to verify that the racing simulation is properly installed
and all components can be imported.
"""

import sys
import traceback

def test_python_version():
    """Test Python version compatibility."""
    print("Testing Python version...")
    if sys.version_info < (3, 8):
        print(f"❌ Python 3.8+ required, found {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def test_dependencies():
    """Test required dependencies."""
    print("\nTesting dependencies...")
    
    dependencies = [
        ('panda3d', 'Panda3D 3D Engine'),
        ('numpy', 'NumPy'),
        ('scipy', 'SciPy'),
        ('matplotlib', 'Matplotlib'),
        ('pygame', 'Pygame'),
        ('noise', 'Noise'),
        ('PIL', 'Pillow')
    ]
    
    failed = []
    
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} - Not installed")
            failed.append(module)
    
    if failed:
        print(f"\nMissing dependencies: {', '.join(failed)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    return True

def test_module_imports():
    """Test simulation module imports."""
    print("\nTesting simulation modules...")
    
    modules = [
        ('utils.math_utils', 'Mathematical utilities'),
        ('utils.performance', 'Performance monitoring'),
        ('physics.car_physics', 'Car physics'),
        ('physics.tire_physics', 'Tire physics'),
        ('physics.suspension', 'Suspension system'),
        ('physics.aerodynamics', 'Aerodynamics'),
        ('physics.engine', 'Engine system'),
        ('world.terrain_generator', 'Terrain generation'),
        ('world.environment', 'Environment system'),
        ('vehicle.car', 'Vehicle system'),
        ('vehicle.telemetry', 'Telemetry system'),
        ('input.input_manager', 'Input management')
    ]
    
    failed = []
    
    for module, name in modules:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError as e:
            print(f"❌ {name} - {e}")
            failed.append(module)
        except Exception as e:
            print(f"⚠️  {name} - Import warning: {e}")
    
    if failed:
        print(f"\nFailed imports: {', '.join(failed)}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic functionality."""
    print("\nTesting basic functionality...")
    
    try:
        # Test math utilities
        from utils.math_utils import MathUtils
        result = MathUtils.clamp(1.5, 0, 1)
        assert result == 1.0
        print("✅ Math utilities")
        
        # Test physics initialization
        from physics.car_physics import CarPhysics
        physics = CarPhysics()
        assert physics.mass > 0
        print("✅ Physics initialization")
        
        # Test tire physics
        from physics.tire_physics import TirePhysics
        tire = TirePhysics()
        assert tire.tire_radius > 0
        print("✅ Tire physics")
        
        print("✅ Basic functionality tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        traceback.print_exc()
        return False

def test_panda3d_setup():
    """Test Panda3D specific setup."""
    print("\nTesting Panda3D setup...")
    
    try:
        from panda3d.core import Vec3, Quat
        from panda3d.bullet import BulletWorld
        
        # Test basic Panda3D objects
        vec = Vec3(1, 2, 3)
        quat = Quat()
        world = BulletWorld()
        
        print("✅ Panda3D core functionality")
        return True
        
    except Exception as e:
        print(f"❌ Panda3D test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Advanced Car Racing Simulation - Installation Test")
    print("=" * 55)
    
    tests = [
        test_python_version,
        test_dependencies,
        test_module_imports,
        test_basic_functionality,
        test_panda3d_setup
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 55)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The simulation is ready to run.")
        print("\nTo start the simulation:")
        print("  python main.py")
        print("  or")
        print("  python launch.py")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check Python version (3.8+ required)")
        print("3. Verify all files are present in the project directory")
        return 1

if __name__ == "__main__":
    sys.exit(main())

"""
Environmental System
===================

Weather, lighting, and atmospheric effects for the racing simulation.
"""

import math
import numpy as np
from panda3d.core import *
from direct.task import Task
import random

class Environment:
    """Environmental effects and weather system."""
    
    def __init__(self, game_engine):
        """Initialize environment system."""
        self.game_engine = game_engine
        
        # Time of day
        self.time_of_day = 12.0  # 24-hour format
        self.time_speed = 1.0    # Real-time multiplier
        
        # Weather parameters
        self.weather_type = "clear"  # clear, cloudy, rain, fog
        self.cloud_coverage = 0.0    # 0-1
        self.precipitation = 0.0     # 0-1
        self.wind_speed = 5.0        # m/s
        self.wind_direction = 0.0    # radians
        self.humidity = 0.5          # 0-1
        self.temperature = 20.0      # Celsius
        self.pressure = 1013.25      # hPa
        
        # Visibility
        self.visibility = 10000.0    # meters
        self.fog_density = 0.0       # 0-1
        
        # Sky and lighting
        self.sky_node = None
        self.sun_angle = 0.0         # Sun elevation angle
        self.sun_azimuth = 0.0       # Sun azimuth angle
        
        # Particle systems
        self.rain_particles = None
        self.dust_particles = None
        
        # Surface conditions
        self.track_wetness = 0.0     # 0-1 (affects tire grip)
        self.track_temperature = 25.0  # Celsius
        
        print("Environment system initialized")
    
    def setup_sky(self):
        """Create procedural sky dome."""
        # Create sky sphere
        sky_sphere = self.game_engine.loader.loadModel("environment")
        if not sky_sphere:
            # Create procedural sky sphere
            sky_sphere = self.create_sky_sphere()
        
        sky_sphere.reparentTo(self.game_engine.render)
        sky_sphere.setScale(5000)  # Large enough to encompass terrain
        sky_sphere.setBin("background", 0)
        sky_sphere.setDepthWrite(False)
        sky_sphere.setDepthTest(False)
        
        self.sky_node = sky_sphere
        
        # Update sky based on time and weather
        self.update_sky_appearance()
    
    def create_sky_sphere(self):
        """Create a procedural sky sphere."""
        # Create sphere geometry
        sky_geom = CardMaker("sky")
        sky_geom.setFrame(-1, 1, -1, 1)
        sky_card = self.game_engine.render.attachNewNode(sky_geom.generate())
        
        # Apply sky shader
        sky_shader = self.create_sky_shader()
        sky_card.setShader(sky_shader)
        
        return sky_card
    
    def create_sky_shader(self):
        """Create sky rendering shader."""
        # Simplified sky shader (would be more complex in full implementation)
        vertex_shader = """
        #version 330
        
        in vec4 p3d_Vertex;
        in vec2 p3d_MultiTexCoord0;
        
        uniform mat4 p3d_ModelViewProjectionMatrix;
        
        out vec2 texcoord;
        out vec3 world_pos;
        
        void main() {
            gl_Position = p3d_ModelViewProjectionMatrix * p3d_Vertex;
            texcoord = p3d_MultiTexCoord0;
            world_pos = p3d_Vertex.xyz;
        }
        """
        
        fragment_shader = """
        #version 330
        
        in vec2 texcoord;
        in vec3 world_pos;
        
        uniform float time_of_day;
        uniform float cloud_coverage;
        uniform vec3 sun_direction;
        
        out vec4 fragColor;
        
        void main() {
            vec3 sky_color = vec3(0.5, 0.7, 1.0);  // Base sky blue
            
            // Simple gradient based on height
            float height_factor = normalize(world_pos).z;
            sky_color = mix(vec3(0.8, 0.9, 1.0), sky_color, height_factor);
            
            // Time of day effects
            float day_factor = sin(time_of_day * 3.14159 / 12.0);
            if (day_factor < 0) {
                sky_color *= 0.1;  // Night
            }
            
            fragColor = vec4(sky_color, 1.0);
        }
        """
        
        shader = Shader.make(Shader.SL_GLSL, vertex_shader, fragment_shader)
        return shader
    
    def update_sky_appearance(self):
        """Update sky appearance based on time and weather."""
        if not self.sky_node:
            return
        
        # Calculate sun position
        self.calculate_sun_position()
        
        # Update sky shader uniforms
        self.sky_node.setShaderInput("time_of_day", self.time_of_day)
        self.sky_node.setShaderInput("cloud_coverage", self.cloud_coverage)
        
        sun_dir = Vec3(
            math.cos(self.sun_azimuth) * math.cos(self.sun_angle),
            math.sin(self.sun_azimuth) * math.cos(self.sun_angle),
            math.sin(self.sun_angle)
        )
        self.sky_node.setShaderInput("sun_direction", sun_dir)
    
    def calculate_sun_position(self):
        """Calculate sun position based on time of day."""
        # Simplified sun position calculation
        hour_angle = (self.time_of_day - 12.0) * 15.0  # Degrees from noon
        self.sun_azimuth = math.radians(hour_angle)
        
        # Sun elevation (simplified)
        if 6 <= self.time_of_day <= 18:  # Daytime
            noon_factor = 1.0 - abs(self.time_of_day - 12.0) / 6.0
            self.sun_angle = math.radians(60.0 * noon_factor)  # Max 60 degrees
        else:  # Nighttime
            self.sun_angle = math.radians(-20.0)  # Below horizon
    
    def update_lighting(self):
        """Update lighting based on time and weather."""
        if not hasattr(self.game_engine, 'sun'):
            return
        
        # Calculate sun intensity
        sun_intensity = max(0.0, math.sin(self.sun_angle))
        
        # Weather effects on lighting
        weather_factor = 1.0
        if self.weather_type == "cloudy":
            weather_factor = 0.7
        elif self.weather_type == "rain":
            weather_factor = 0.5
        elif self.weather_type == "fog":
            weather_factor = 0.3
        
        final_intensity = sun_intensity * weather_factor
        
        # Update sun light
        sun_color = VBase4(1.0, 0.95, 0.8, 1.0) * final_intensity
        self.game_engine.sun.setColor(sun_color)
        
        # Update sun direction
        sun_dir = Vec3(
            math.cos(self.sun_azimuth) * math.cos(self.sun_angle),
            math.sin(self.sun_azimuth) * math.cos(self.sun_angle),
            math.sin(self.sun_angle)
        )
        self.game_engine.sun.setDirection(sun_dir)
        
        # Update ambient light
        ambient_intensity = 0.2 + final_intensity * 0.3
        ambient_color = VBase4(0.3, 0.3, 0.4, 1.0) * ambient_intensity
        self.game_engine.ambient.setColor(ambient_color)
    
    def setup_weather(self):
        """Initialize weather effects."""
        self.setup_rain_particles()
        self.setup_fog()
        self.update_weather_effects()
    
    def setup_rain_particles(self):
        """Create rain particle system."""
        # This would create a particle system for rain
        # Simplified implementation
        pass
    
    def setup_fog(self):
        """Setup fog effects."""
        # Configure fog based on weather
        if self.weather_type == "fog" or self.fog_density > 0:
            fog = Fog("fog")
            fog.setColor(0.8, 0.8, 0.9)
            fog.setExpDensity(self.fog_density * 0.001)
            
            fog_np = self.game_engine.render.attachNewNode(fog)
            self.game_engine.render.setFog(fog_np)
        else:
            self.game_engine.render.clearFog()
    
    def update_weather_effects(self):
        """Update weather-based effects."""
        # Update track conditions
        if self.weather_type == "rain":
            self.track_wetness = min(1.0, self.track_wetness + 0.01)
            self.precipitation = 0.8
        else:
            self.track_wetness = max(0.0, self.track_wetness - 0.005)
            self.precipitation = 0.0
        
        # Update tire physics based on track wetness
        if hasattr(self.game_engine, 'car') and hasattr(self.game_engine.car, 'physics'):
            tire_physics = self.game_engine.car.physics.tire_physics
            if self.track_wetness > 0.1:
                tire_physics.set_surface_conditions("wet")
            else:
                tire_physics.set_surface_conditions("dry")
    
    def set_weather(self, weather_type):
        """Set weather type."""
        self.weather_type = weather_type
        
        if weather_type == "clear":
            self.cloud_coverage = 0.1
            self.precipitation = 0.0
            self.fog_density = 0.0
        elif weather_type == "cloudy":
            self.cloud_coverage = 0.7
            self.precipitation = 0.0
            self.fog_density = 0.0
        elif weather_type == "rain":
            self.cloud_coverage = 0.9
            self.precipitation = 0.8
            self.fog_density = 0.1
        elif weather_type == "fog":
            self.cloud_coverage = 0.8
            self.precipitation = 0.0
            self.fog_density = 0.5
        
        self.update_weather_effects()
        self.setup_fog()
        
        print(f"Weather set to: {weather_type}")
    
    def set_time_of_day(self, hour):
        """Set time of day (0-24 hours)."""
        self.time_of_day = hour % 24
        self.update_sky_appearance()
        self.update_lighting()
        
        print(f"Time set to: {hour:02.1f}:00")
    
    def get_wind_vector(self):
        """Get wind velocity vector."""
        return Vec3(
            self.wind_speed * math.cos(self.wind_direction),
            self.wind_speed * math.sin(self.wind_direction),
            0
        )
    
    def get_environmental_data(self):
        """Get environmental data for telemetry."""
        return {
            'time_of_day': self.time_of_day,
            'weather': self.weather_type,
            'temperature': self.temperature,
            'track_temperature': self.track_temperature,
            'humidity': self.humidity,
            'pressure': self.pressure,
            'wind_speed': self.wind_speed,
            'wind_direction': math.degrees(self.wind_direction),
            'track_wetness': self.track_wetness,
            'visibility': self.visibility
        }
    
    def update(self, dt):
        """Update environment system."""
        # Update time of day
        self.time_of_day += (dt * self.time_speed) / 3600.0  # Convert to hours
        self.time_of_day = self.time_of_day % 24
        
        # Update lighting
        self.update_lighting()
        
        # Update weather effects
        self.update_weather_effects()
        
        # Update track temperature based on sun and air temperature
        sun_heating = max(0, math.sin(self.sun_angle)) * 10.0
        self.track_temperature = self.temperature + sun_heating

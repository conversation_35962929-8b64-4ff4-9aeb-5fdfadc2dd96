# Advanced Car Racing Simulation

A comprehensive racing simulation built with Python and Panda3D, featuring realistic physics, procedural world generation, and advanced car dynamics that rivals professional racing simulators like iRacing and Gran Turismo.

## Features

### Core Simulation
- **Advanced Physics Engine**: Multi-body dynamics with realistic car behavior
- **Realistic Tire Physics**: Pacejka tire model with temperature and wear simulation
- **Sophisticated Suspension**: Progressive springs, dampers, and anti-roll bars
- **Engine Modeling**: Realistic torque curves, transmission, and drivetrain simulation
- **Aerodynamics**: Downforce, drag, and ground effect modeling
- **Weather System**: Dynamic weather affecting track conditions and visibility

### World Generation
- **Procedural Terrain**: Large-scale terrain generation using Perlin noise
- **Dynamic Tracks**: Multiple interconnected racing circuits
- **Environmental Effects**: Day/night cycle, weather, and atmospheric conditions
- **Surface Conditions**: Variable grip levels and track temperature

### Advanced Features
- **Comprehensive Telemetry**: Real-time data logging and analysis
- **Performance Monitoring**: Adaptive quality settings for optimal performance
- **Input Support**: Keyboard, gamepad, and steering wheel compatibility
- **Multiple Camera Modes**: Chase, cockpit, hood, and wheel cameras
- **Force Feedback**: Realistic steering wheel feedback (when supported)

## Installation

### Prerequisites
- Python 3.8 or higher
- Windows, macOS, or Linux

### Setup
1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Dependencies
- **Panda3D**: 3D engine and physics
- **NumPy**: Mathematical computations
- **SciPy**: Scientific computing
- **Matplotlib**: Data visualization and telemetry graphs
- **Pygame**: Additional input support
- **Noise**: Procedural terrain generation
- **Pillow**: Image processing

## Quick Start

1. **Run the simulation**:
   ```bash
   python main.py
   ```

2. **Controls**:
   - **W/↑**: Throttle
   - **S/↓**: Brake
   - **A/←, D/→**: Steering
   - **Space**: Clutch
   - **Q/E**: Gear down/up
   - **C**: Change camera mode
   - **R**: Reset car
   - **X**: Handbrake

3. **Gamepad Support**: Xbox controllers are automatically detected and supported

## Architecture

### Physics System (`physics/`)
- `car_physics.py`: Main vehicle dynamics integration
- `tire_physics.py`: Advanced tire modeling with Pacejka equations
- `suspension.py`: Suspension dynamics and geometry
- `aerodynamics.py`: Aerodynamic forces and moments
- `engine.py`: Engine and drivetrain simulation

### World System (`world/`)
- `terrain_generator.py`: Procedural terrain and track generation
- `environment.py`: Weather, lighting, and atmospheric effects

### Vehicle System (`vehicle/`)
- `car.py`: Main car class integrating all systems
- `telemetry.py`: Data logging and performance analysis

### Input System (`input/`)
- `input_manager.py`: Multi-device input handling with force feedback

### Utilities (`utils/`)
- `math_utils.py`: Mathematical functions for physics calculations
- `performance.py`: Performance monitoring and optimization

## Advanced Usage

### Telemetry and Data Analysis
The simulation includes comprehensive telemetry logging:

```python
# Access telemetry data
telemetry = game_engine.telemetry
performance_data = telemetry.get_performance_summary()

# Export data to CSV
telemetry.export_to_csv('session_data.csv', 'vehicle')

# Create real-time graphs
telemetry.create_performance_graph('speed_kmh', duration=60)
```

### Physics Tuning
Adjust vehicle parameters for different handling characteristics:

```python
# Adjust suspension settings
car.physics.suspension.adjust_suspension_settings(
    spring_rate_mult=1.2,  # Stiffer springs
    damping_mult=0.8,      # Less damping
    arb_mult=1.5           # Stiffer anti-roll bars
)

# Modify aerodynamics
car.physics.aerodynamics.adjust_aerodynamic_settings(
    front_wing_angle=5,    # More front downforce
    rear_wing_angle=8      # More rear downforce
)
```

### Environmental Controls
Change weather and time conditions:

```python
# Set weather conditions
game_engine.environment.set_weather('rain')
game_engine.environment.set_time_of_day(18.5)  # 6:30 PM

# Adjust environmental parameters
game_engine.environment.temperature = 15.0  # Celsius
game_engine.environment.wind_speed = 10.0   # m/s
```

## Performance Optimization

The simulation includes adaptive performance monitoring:

- **Automatic Quality Adjustment**: Graphics quality adapts based on frame rate
- **Physics Optimization**: Variable timestep physics for stability
- **Memory Management**: Efficient data structures and garbage collection
- **Profiling Tools**: Built-in performance analysis and bottleneck detection

Target performance: 60+ FPS on modern hardware

## Technical Specifications

### Physics Accuracy
- **Tire Model**: Pacejka Magic Formula with temperature and wear effects
- **Suspension**: Multi-link geometry with progressive rates
- **Aerodynamics**: CFD-inspired downforce and drag calculations
- **Engine**: Realistic torque curves and transmission modeling
- **Integration**: 4th-order Runge-Kutta with adaptive timestep

### Simulation Fidelity
- **Update Rate**: 60-120 Hz physics simulation
- **Tire Resolution**: Individual wheel dynamics and slip calculations
- **Weather Effects**: Real-time surface condition changes
- **Telemetry Rate**: 60 Hz data logging with microsecond precision

## Extending the Simulation

### Adding New Vehicles
Create new car configurations by modifying physics parameters:

```python
# Create a new car type
sports_car = CarPhysics(
    mass=1200,           # kg
    wheelbase=2.5,       # meters
    track_width=1.6      # meters
)

# Customize engine characteristics
sports_car.engine.max_power = 400000  # 400 kW
sports_car.engine.max_torque = 500    # Nm
```

### Custom Track Generation
Implement custom track layouts:

```python
# Generate custom track
custom_track = terrain_generator.generate_track_path(
    start_pos=Vec3(0, 0, 0),
    length=3000,         # meters
    complexity=0.7       # 0-1 complexity factor
)
```

## Troubleshooting

### Common Issues
1. **Low Frame Rate**: Reduce terrain detail or graphics quality
2. **Physics Instability**: Increase physics substeps or reduce timestep
3. **Input Lag**: Check input device drivers and reduce input smoothing
4. **Memory Usage**: Enable data compression in telemetry system

### Performance Tuning
- Adjust `terrain_resolution` in `terrain_generator.py`
- Modify `physics_substeps` in performance settings
- Enable/disable real-time telemetry logging
- Reduce particle effects and visual complexity

## Contributing

This simulation is designed for educational and research purposes. Key areas for contribution:
- Additional tire models and validation data
- Enhanced aerodynamic calculations
- More sophisticated weather effects
- AI opponent implementation
- VR/AR integration

## License

MIT License - See LICENSE file for details

## Acknowledgments

- Pacejka tire model research and implementation
- Panda3D community for engine support
- Racing simulation community for physics validation
- Open-source scientific computing libraries

---

**Note**: This is a comprehensive racing simulation designed for educational and research purposes. The physics models are based on real-world automotive engineering principles and provide realistic vehicle dynamics suitable for driver training and vehicle development applications.

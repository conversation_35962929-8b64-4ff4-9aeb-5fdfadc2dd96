"""
Advanced Tire Physics System
============================

Realistic tire modeling with grip, slip, and temperature effects.
"""

import math
import numpy as np
from panda3d.core import Vec3
from utils.math_utils import MathUtils

class TirePhysics:
    """Advanced tire physics simulation."""
    
    def __init__(self):
        """Initialize tire physics parameters."""
        # Tire physical properties
        self.tire_radius = 0.32  # meters
        self.tire_width = 0.225  # meters
        self.tire_mass = 25.0    # kg per tire
        
        # Friction coefficients
        self.dry_friction = 1.1
        self.wet_friction = 0.7
        self.current_friction = self.dry_friction
        
        # Pacejka tire model parameters
        self.pacejka_params = {
            'B': 10.0,   # Stiffness factor
            'C': 1.3,    # Shape factor
            'D': 1.0,    # Peak factor (will be multiplied by normal force)
            'E': 0.97    # Curvature factor
        }
        
        # Tire temperature model
        self.tire_temp = 80.0  # Celsius
        self.optimal_temp = 90.0
        self.temp_friction_curve = self.create_temperature_curve()
        
        # Wear model
        self.tire_wear = 0.0  # 0 = new, 1 = completely worn
        self.wear_rate = 0.0001  # per second of sliding
        
        # Rolling resistance
        self.rolling_resistance_coeff = 0.015
        
        print("Tire physics initialized")
    
    def create_temperature_curve(self):
        """Create tire temperature vs friction curve."""
        # Temperature points (Celsius)
        temps = np.array([20, 40, 60, 80, 90, 100, 120, 140])
        # Friction multipliers
        friction_mults = np.array([0.7, 0.8, 0.9, 0.95, 1.0, 0.98, 0.9, 0.8])
        
        return {'temps': temps, 'friction_mults': friction_mults}
    
    def get_temperature_friction_multiplier(self):
        """Get friction multiplier based on tire temperature."""
        temps = self.temp_friction_curve['temps']
        mults = self.temp_friction_curve['friction_mults']
        
        # Interpolate
        return np.interp(self.tire_temp, temps, mults)
    
    def get_wear_friction_multiplier(self):
        """Get friction multiplier based on tire wear."""
        # Linear degradation from 1.0 to 0.6 as wear goes from 0 to 1
        return 1.0 - (self.tire_wear * 0.4)
    
    def calculate_effective_friction(self, normal_force):
        """Calculate effective friction coefficient."""
        base_friction = self.current_friction
        
        # Apply temperature effects
        temp_mult = self.get_temperature_friction_multiplier()
        
        # Apply wear effects
        wear_mult = self.get_wear_friction_multiplier()
        
        # Load sensitivity (friction decreases slightly with higher loads)
        load_mult = 1.0 - (normal_force - 5000) / 50000 * 0.1
        load_mult = MathUtils.clamp(load_mult, 0.8, 1.0)
        
        return base_friction * temp_mult * wear_mult * load_mult
    
    def pacejka_lateral_force(self, slip_angle, normal_force):
        """
        Calculate lateral tire force using Pacejka tire model.
        
        Args:
            slip_angle: Tire slip angle in radians
            normal_force: Normal force on tire in Newtons
        
        Returns:
            Lateral force in Newtons
        """
        if normal_force <= 0:
            return 0.0
        
        # Get effective friction
        friction = self.calculate_effective_friction(normal_force)
        
        # Pacejka parameters
        B = self.pacejka_params['B']
        C = self.pacejka_params['C']
        D = friction * normal_force  # Peak force
        E = self.pacejka_params['E']
        
        # Calculate lateral force
        BX = B * slip_angle
        lateral_force = D * math.sin(C * math.atan(BX - E * (BX - math.atan(BX))))
        
        return lateral_force
    
    def pacejka_longitudinal_force(self, slip_ratio, normal_force):
        """
        Calculate longitudinal tire force using Pacejka tire model.
        
        Args:
            slip_ratio: Tire slip ratio (-1 to 1)
            normal_force: Normal force on tire in Newtons
        
        Returns:
            Longitudinal force in Newtons
        """
        if normal_force <= 0:
            return 0.0
        
        # Get effective friction
        friction = self.calculate_effective_friction(normal_force)
        
        # Modified Pacejka parameters for longitudinal force
        B = self.pacejka_params['B'] * 0.8  # Slightly different for longitudinal
        C = 1.65  # Different shape factor
        D = friction * normal_force
        E = 0.97
        
        # Calculate longitudinal force
        BX = B * slip_ratio
        long_force = D * math.sin(C * math.atan(BX - E * (BX - math.atan(BX))))
        
        return long_force
    
    def calculate_combined_forces(self, slip_angle, slip_ratio, normal_force):
        """
        Calculate combined lateral and longitudinal forces.
        Uses friction circle concept.
        """
        # Calculate individual forces
        pure_lateral = self.pacejka_lateral_force(slip_angle, normal_force)
        pure_longitudinal = self.pacejka_longitudinal_force(slip_ratio, normal_force)
        
        # Friction circle - combined forces cannot exceed total friction
        max_force = self.calculate_effective_friction(normal_force) * normal_force
        
        # Calculate combined force magnitude
        combined_magnitude = math.sqrt(pure_lateral**2 + pure_longitudinal**2)
        
        if combined_magnitude > max_force:
            # Scale down forces to stay within friction circle
            scale_factor = max_force / combined_magnitude
            lateral_force = pure_lateral * scale_factor
            longitudinal_force = pure_longitudinal * scale_factor
        else:
            lateral_force = pure_lateral
            longitudinal_force = pure_longitudinal
        
        return lateral_force, longitudinal_force
    
    def calculate_tire_force(self, wheel_velocity, wheel_speed, wheel_angle, normal_force):
        """
        Calculate total tire force vector.
        
        Args:
            wheel_velocity: Velocity of wheel contact patch (Vec3)
            wheel_speed: Rotational speed of wheel (m/s)
            wheel_angle: Steering angle of wheel (radians)
            normal_force: Normal force on tire (Newtons)
        
        Returns:
            Force vector in wheel coordinates (Vec3)
        """
        if normal_force <= 0:
            return Vec3(0, 0, 0)
        
        # Transform velocity to tire coordinate system
        # Tire coordinates: X = lateral, Y = longitudinal, Z = normal
        cos_angle = math.cos(wheel_angle)
        sin_angle = math.sin(wheel_angle)
        
        # Velocity in tire coordinates
        tire_vel_x = wheel_velocity.x * cos_angle + wheel_velocity.y * sin_angle
        tire_vel_y = -wheel_velocity.x * sin_angle + wheel_velocity.y * cos_angle
        
        # Calculate slip angle
        if abs(tire_vel_y) < 0.1:
            slip_angle = 0.0
        else:
            slip_angle = math.atan2(tire_vel_x, abs(tire_vel_y))
        
        # Calculate slip ratio
        vehicle_speed = abs(tire_vel_y)
        if vehicle_speed < 0.1:
            slip_ratio = 0.0
        else:
            slip_ratio = (wheel_speed - vehicle_speed) / vehicle_speed
            slip_ratio = MathUtils.clamp(slip_ratio, -1.0, 1.0)
        
        # Calculate forces
        lateral_force, longitudinal_force = self.calculate_combined_forces(
            slip_angle, slip_ratio, normal_force
        )
        
        # Add rolling resistance
        rolling_resistance = MathUtils.calculate_rolling_resistance(
            normal_force, self.rolling_resistance_coeff
        )
        
        # Apply rolling resistance opposite to motion
        if tire_vel_y > 0:
            longitudinal_force -= rolling_resistance
        elif tire_vel_y < 0:
            longitudinal_force += rolling_resistance
        
        # Force in tire coordinates
        tire_force = Vec3(lateral_force, longitudinal_force, 0)
        
        # Transform back to wheel coordinates
        wheel_force_x = tire_force.x * cos_angle - tire_force.y * sin_angle
        wheel_force_y = tire_force.x * sin_angle + tire_force.y * cos_angle
        
        # Update tire temperature based on slip
        self.update_tire_temperature(slip_angle, slip_ratio, normal_force)
        
        # Update tire wear
        self.update_tire_wear(slip_angle, slip_ratio)
        
        return Vec3(wheel_force_x, wheel_force_y, 0)
    
    def update_tire_temperature(self, slip_angle, slip_ratio, normal_force):
        """Update tire temperature based on slip and load."""
        # Heat generation from slip
        slip_energy = abs(slip_angle) * 10 + abs(slip_ratio) * 5
        load_factor = normal_force / 5000.0  # Normalize to typical load
        
        heat_generation = slip_energy * load_factor * 0.1
        
        # Heat dissipation (cooling)
        ambient_temp = 25.0  # Celsius
        cooling_rate = (self.tire_temp - ambient_temp) * 0.01
        
        # Update temperature
        self.tire_temp += heat_generation - cooling_rate
        self.tire_temp = MathUtils.clamp(self.tire_temp, ambient_temp, 150.0)
    
    def update_tire_wear(self, slip_angle, slip_ratio):
        """Update tire wear based on slip."""
        # Wear increases with slip
        wear_increase = (abs(slip_angle) + abs(slip_ratio)) * self.wear_rate
        self.tire_wear = min(1.0, self.tire_wear + wear_increase)
    
    def set_surface_conditions(self, surface_type="dry"):
        """Set surface conditions affecting friction."""
        if surface_type == "dry":
            self.current_friction = self.dry_friction
        elif surface_type == "wet":
            self.current_friction = self.wet_friction
        elif surface_type == "ice":
            self.current_friction = 0.1
        elif surface_type == "gravel":
            self.current_friction = 0.6
        else:
            self.current_friction = self.dry_friction
    
    def get_tire_info(self):
        """Get current tire information for telemetry."""
        return {
            'temperature': self.tire_temp,
            'wear': self.tire_wear,
            'friction': self.calculate_effective_friction(5000),  # At nominal load
            'optimal_temp_diff': abs(self.tire_temp - self.optimal_temp)
        }
